import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface WebSocketContextType {
  isConnected: boolean;
  isConnecting: boolean;
  connect: () => void;
  disconnect: () => void;
  sendMessage: (message: any) => void;
  lastMessage: any;
  sentMessage: any;
  connectionError: string | null;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

interface WebSocketProviderProps {
  children: ReactNode;
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const [ws, setWs] = useState<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [lastMessage, setLastMessage] = useState<any>(null);
  const [sentMessage, setSentMessage] = useState<any>(null);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  const connect = () => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    setIsConnecting(true);
    setConnectionError(null);

    try {
      const websocket = new WebSocket('ws://localhost:8765');
      
      websocket.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setIsConnecting(false);
        setConnectionError(null);
      };

      websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          setLastMessage(message);
          console.log('Received message:', message);
        } catch (error) {
          console.error('Error parsing message:', error);
        }
      };

      websocket.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        setIsConnecting(false);
        setWs(null);
        
        if (event.code !== 1000) {
          setConnectionError(`Connection closed: ${event.reason || 'Unknown error'}`);
        }
      };

      websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionError('Connection failed');
        setIsConnecting(false);
      };

      setWs(websocket);
    } catch (error) {
      console.error('Error creating WebSocket:', error);
      setConnectionError('Failed to create connection');
      setIsConnecting(false);
    }
  };

  const disconnect = () => {
    if (ws) {
      ws.close(1000, 'User disconnected');
      setWs(null);
    }
    setIsConnected(false);
    setIsConnecting(false);
    setConnectionError(null);
  };

  const sendMessage = (message: any) => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      const messageStr = JSON.stringify(message);
      ws.send(messageStr);
      console.log('Sent message:', message);
      console.log('Message string length:', messageStr.length);
      setSentMessage(message);
    } else {
      console.error('WebSocket is not connected. ReadyState:', ws?.readyState);
      setConnectionError('Cannot send message: not connected');
    }
  };

  // Auto-connect on mount
  useEffect(() => {
    connect();
    
    // Cleanup on unmount
    return () => {
      if (ws) {
        ws.close(1000, 'Component unmounted');
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const value: WebSocketContextType = {
    isConnected,
    isConnecting,
    connect,
    disconnect,
    sendMessage,
    lastMessage,
    sentMessage,
    connectionError,
  };

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
};

export const useWebSocket = (): WebSocketContextType => {
  const context = useContext(WebSocketContext);
  if (context === undefined) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
}; 