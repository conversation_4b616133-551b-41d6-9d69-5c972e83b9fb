import React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from 'react';

interface AudioState {
  isPlaying: boolean;
  currentAudio: HTMLAudioElement | null;
  canInterrupt: boolean;
}

interface WebSocketContextType {
  isConnected: boolean;
  isConnecting: boolean;
  connect: () => void;
  disconnect: () => void;
  sendMessage: (message: any) => void;
  lastMessage: any;
  sentMessage: any;
  connectionError: string | null;
  // Audio state management
  audioState: AudioState;
  playAudio: (audioData: string) => Promise<void>;
  stopAudio: () => void;
  setCanInterrupt: (canInterrupt: boolean) => void;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

interface WebSocketProviderProps {
  children: ReactNode;
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const [ws, setWs] = useState<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [lastMessage, setLastMessage] = useState<any>(null);
  const [sentMessage, setSentMessage] = useState<any>(null);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  // Audio state management
  const [audioState, setAudioState] = useState<AudioState>({
    isPlaying: false,
    currentAudio: null,
    canInterrupt: true
  });
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const connect = () => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    setIsConnecting(true);
    setConnectionError(null);

    try {
      const websocket = new WebSocket('ws://localhost:8765');
      
      websocket.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setIsConnecting(false);
        setConnectionError(null);
      };

      websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          setLastMessage(message);
          console.log('Received message:', message);
        } catch (error) {
          console.error('Error parsing message:', error);
        }
      };

      websocket.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        setIsConnecting(false);
        setWs(null);
        
        if (event.code !== 1000) {
          setConnectionError(`Connection closed: ${event.reason || 'Unknown error'}`);
        }
      };

      websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionError('Connection failed');
        setIsConnecting(false);
      };

      setWs(websocket);
    } catch (error) {
      console.error('Error creating WebSocket:', error);
      setConnectionError('Failed to create connection');
      setIsConnecting(false);
    }
  };

  const disconnect = () => {
    if (ws) {
      ws.close(1000, 'User disconnected');
      setWs(null);
    }
    setIsConnected(false);
    setIsConnecting(false);
    setConnectionError(null);
    // Stop any playing audio when disconnecting
    stopAudio();
  };

  const sendMessage = (message: any) => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      const messageStr = JSON.stringify(message);
      ws.send(messageStr);
      console.log('Sent message:', message);
      console.log('Message string length:', messageStr.length);
      setSentMessage(message);
    } else {
      console.error('WebSocket is not connected. ReadyState:', ws?.readyState);
      setConnectionError('Cannot send message: not connected');
    }
  };

  // Audio management functions
  const playAudio = async (audioData: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      try {
        console.log('🎵 Playing audio response, data length:', audioData.length);

        // Stop any currently playing audio
        stopAudio();

        // Convert base64 to blob
        const audioBlob = new Blob([Uint8Array.from(atob(audioData), c => c.charCodeAt(0))], { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);

        console.log('🎵 Audio blob created, size:', audioBlob.size, 'bytes');

        // Create audio element
        const audio = new Audio(audioUrl);
        audioRef.current = audio;

        audio.onloadstart = () => {
          console.log('🎵 Audio loading started');
          setAudioState(prev => ({
            ...prev,
            isPlaying: true,
            currentAudio: audio
          }));
        };

        audio.oncanplay = () => {
          console.log('🎵 Audio can play, starting playback');
          audio.play().then(() => {
            console.log('🎵 Audio playback started successfully');
          }).catch(error => {
            console.error('❌ Error playing audio:', error);
            setAudioState(prev => ({
              ...prev,
              isPlaying: false,
              currentAudio: null
            }));
            reject(error);
          });
        };

        audio.onended = () => {
          console.log('🎵 Audio playback ended');
          setAudioState(prev => ({
            ...prev,
            isPlaying: false,
            currentAudio: null
          }));
          URL.revokeObjectURL(audioUrl);
          audioRef.current = null;

          // Send signal to backend that audio playback ended
          if (ws && ws.readyState === WebSocket.OPEN) {
            const endMessage = { type: 'signal', data: 'AUDIO_PLAYBACK_ENDED' };
            ws.send(JSON.stringify(endMessage));
            console.log('📡 Sent audio playback ended signal');
          }

          resolve();
        };

        audio.onerror = (error) => {
          console.error('❌ Audio error:', error);
          setAudioState(prev => ({
            ...prev,
            isPlaying: false,
            currentAudio: null
          }));
          URL.revokeObjectURL(audioUrl);
          audioRef.current = null;
          reject(error);
        };

        console.log('🎵 Audio element created, loading audio...');
      } catch (error) {
        console.error('❌ Error playing audio response:', error);
        setAudioState(prev => ({
          ...prev,
          isPlaying: false,
          currentAudio: null
        }));
        reject(error);
      }
    });
  };

  const stopAudio = () => {
    if (audioRef.current) {
      console.log('🛑 Stopping current audio playback');
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      audioRef.current = null;
    }

    setAudioState(prev => ({
      ...prev,
      isPlaying: false,
      currentAudio: null
    }));
  };

  const setCanInterrupt = (canInterrupt: boolean) => {
    setAudioState(prev => ({
      ...prev,
      canInterrupt
    }));
  };

  // Auto-connect on mount
  useEffect(() => {
    connect();
    
    // Cleanup on unmount
    return () => {
      if (ws) {
        ws.close(1000, 'Component unmounted');
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const value: WebSocketContextType = {
    isConnected,
    isConnecting,
    connect,
    disconnect,
    sendMessage,
    lastMessage,
    sentMessage,
    connectionError,
    // Audio state management
    audioState,
    playAudio,
    stopAudio,
    setCanInterrupt,
  };

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
};

export const useWebSocket = (): WebSocketContextType => {
  const context = useContext(WebSocketContext);
  if (context === undefined) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
}; 