import React from 'react';
import './App.css';
import VoiceBotTester from './components/VoiceBotTester';
import ConnectionStatus from './components/ConnectionStatus';
import ConversationLog from './components/ConversationLog';
import NormalConversationSection from './components/NormalConversationSection';
import { WebSocketProvider } from './context/WebSocketContext';

function App() {
  return (
    <WebSocketProvider>
      <div className="App">
        <header className="App-header">
          <h1>🎤 PolicyBazaar Voice Bot</h1>
          <p>Professional Testing Interface</p>
        </header>
        
        <main className="App-main">
          <div className="app-container">
            <div className="left-panel">
              <VoiceBotTester />
            </div>
            <div className="right-panel">
              <ConversationLog />
              <div style={{ marginTop: '20px' }}>
                <NormalConversationSection />
              </div>
            </div>
          </div>
        </main>
        
        <footer className="App-footer">
          <p>PolicyBazaar Voice Bot - Professional Testing Interface</p>
        </footer>
      </div>
    </WebSocketProvider>
  );
}

export default App;
