.App {
  text-align: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
  color: white;
  display: flex;
  flex-direction: column;
  position: relative;
}

.App::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.App-header {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px 20px;
  backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.App-header h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(45deg, #ffffff, #e8f4fd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.App-header p {
  margin: 5px 0 0 0;
  font-size: 1rem;
  opacity: 0.95;
  color: #e8f4fd;
}

.App-main {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: stretch;
}

.app-container {
  display: flex;
  gap: 20px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  height: calc(100vh - 140px);
}

.left-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 0;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  gap: 20px;
}

.App-footer {
  background: rgba(255, 255, 255, 0.1);
  padding: 10px 20px;
  backdrop-filter: blur(15px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: auto;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

.App-footer p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
  color: #e8f4fd;
}

/* Component Styles */
.component-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(255, 255, 255, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.component-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.component-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Button Styles */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 5px;
}

.btn-primary {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  color: white;
  border: none;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
  background: linear-gradient(45deg, #4facfe, #00f2fe);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.btn-danger {
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
  color: white;
  border: none;
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.btn-success {
  background: linear-gradient(45deg, #51cf66, #40c057);
  color: white;
  border: none;
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(81, 207, 102, 0.4);
}

.btn-warning {
  background: linear-gradient(45deg, #ffd93d, #ff6b6b);
  color: white;
  border: none;
}

.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 217, 61, 0.4);
}

.btn-info {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  color: white;
  border: none;
}

.btn-info:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
}

/* Input Styles */
.input-group {
  margin: 15px 0;
}

.input-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  color: #fff;
}

.input-field {
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.15);
  color: white;
  font-size: 1rem;
  backdrop-filter: blur(15px);
  transition: all 0.3s ease;
}

.input-field:focus {
  outline: none;
  border-color: #4facfe;
  box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.2);
  background: rgba(255, 255, 255, 0.2);
}

.input-field::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

.status-connected {
  background: rgba(81, 207, 102, 0.2);
  color: #51cf66;
  border: 1px solid rgba(81, 207, 102, 0.3);
}

.status-disconnected {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.status-connecting {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

/* Audio Controls */
.audio-controls {
  display: flex;
  gap: 10px;
  margin: 15px 0;
  flex-wrap: wrap;
}

.audio-button {
  padding: 15px 20px;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

.audio-button.recording {
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
  color: white;
  animation: pulse 1.5s infinite;
}

.audio-button.playing {
  background: linear-gradient(45deg, #51cf66, #40c057);
  color: white;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Conversation Log */
.conversation-log {
  height: 400px;
  overflow-y: auto;
  padding: 15px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.log-entry {
  margin: 10px 0;
  padding: 12px;
  border-radius: 8px;
  border-left: 4px solid;
}

.log-entry.user {
  background: rgba(102, 126, 234, 0.1);
  border-left-color: #667eea;
}

.log-entry.bot {
  background: rgba(81, 207, 102, 0.1);
  border-left-color: #51cf66;
}

.log-entry.system {
  background: rgba(255, 193, 7, 0.1);
  border-left-color: #ffc107;
}

.log-entry.error {
  background: rgba(255, 107, 107, 0.1);
  border-left-color: #ff6b6b;
}

.log-timestamp {
  font-size: 0.8rem;
  opacity: 0.7;
  margin-bottom: 5px;
}

.log-content {
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
    height: auto;
  }
  
  .App-header h1 {
    font-size: 1.8rem;
  }
  
  .App-header p {
    font-size: 0.9rem;
  }
  
  .component-title {
    font-size: 1.2rem;
  }
  
  .audio-controls {
    justify-content: center;
  }
  
  .audio-button {
    min-width: 100px;
    padding: 12px 16px;
  }
}

/* Professional Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Smooth transitions */
.component-card {
  transition: all 0.3s ease;
}

.component-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Professional button animations */
.btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn:hover {
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}
