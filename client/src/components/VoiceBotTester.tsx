import React, { useState, useEffect, useRef } from 'react';
import { useWebSocket } from '../context/WebSocketContext';

// Compact Connection Status Component
const CompactConnectionStatus: React.FC = () => {
  const { isConnected, isConnecting } = useWebSocket();

  const getStatusIcon = () => {
    if (isConnecting) return '⏳';
    if (isConnected) return '✅';
    return '❌';
  };

  const getStatusColor = () => {
    if (isConnecting) return '#ffc107';
    if (isConnected) return '#51cf66';
    return '#ff6b6b';
  };

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      padding: '8px 12px',
      background: 'rgba(0, 0, 0, 0.2)',
      borderRadius: '20px',
      fontSize: '0.9rem',
      fontWeight: '600',
      border: `1px solid ${getStatusColor()}40`
    }}>
      <span style={{ color: getStatusColor() }}>{getStatusIcon()}</span>
      <span style={{ color: getStatusColor() }}>
        {isConnecting ? 'Connecting' : isConnected ? 'Connected' : 'Disconnected'}
      </span>
    </div>
  );
};

interface LogEntry {
  id: string;
  timestamp: string;
  type: 'user' | 'bot' | 'system' | 'error';
  content: string;
}

const VoiceBotTester: React.FC = () => {
  const { isConnected, sendMessage } = useWebSocket();
  const [isRecording, setIsRecording] = useState(false);
  const [textInput, setTextInput] = useState('');
  const [audioChunks, setAudioChunks] = useState<Blob[]>([]);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [, setAudioContext] = useState<AudioContext | null>(null);
  const [, setLogs] = useState<LogEntry[]>([]);
  const [isSendingAudio, setIsSendingAudio] = useState(false);
  
  // Use ref to track audio chunks for immediate access
  const audioChunksRef = useRef<Blob[]>([]);

  // Initialize audio context
  useEffect(() => {
    const initAudio = async () => {
      try {
        console.log('Initializing audio system...');
        
        // Check if getUserMedia is supported
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          throw new Error('getUserMedia is not supported in this browser');
        }

        const stream = await navigator.mediaDevices.getUserMedia({ 
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            sampleRate: 16000,
            channelCount: 1
          } 
        });
        
        console.log('Audio stream obtained:', stream);
        
        // Try different MIME types in order of preference
        const mimeTypes = [
          'audio/webm;codecs=opus',
          'audio/webm',
          'audio/mp4',
          'audio/ogg;codecs=opus',
          'audio/wav'
        ];
        
        let selectedMimeType = null;
        for (const mimeType of mimeTypes) {
          if (MediaRecorder.isTypeSupported(mimeType)) {
            selectedMimeType = mimeType;
            console.log(`Using MIME type: ${mimeType}`);
            break;
          }
        }
        
        if (!selectedMimeType) {
          throw new Error('No supported audio MIME type found');
        }
        
        const recorder = new MediaRecorder(stream, {
          mimeType: selectedMimeType
        });
        
        const context = new AudioContext();
        
        setMediaRecorder(recorder);
        setAudioContext(context);
        
        console.log('Audio system initialized successfully');
        addLog('system', `Audio system initialized successfully with MIME type: ${selectedMimeType}`);
      } catch (error) {
        console.error('Error initializing audio:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        addLog('error', `Failed to initialize audio system: ${errorMessage}`);
      }
    };

    initAudio();
  }, []);

  const addLog = (type: 'user' | 'bot' | 'system' | 'error', content: string) => {
    const logEntry: LogEntry = {
      id: Date.now().toString(),
      timestamp: new Date().toLocaleTimeString(),
      type,
      content,
    };
    setLogs(prev => [...prev, logEntry]);
  };

  const startRecording = () => {
    if (!mediaRecorder || !isConnected) {
      console.log('Cannot start recording:', { mediaRecorder: !!mediaRecorder, isConnected });
      addLog('error', `Cannot start recording: MediaRecorder=${!!mediaRecorder}, Connected=${isConnected}`);
      return;
    }

    try {
      // Clear both state and ref
      setAudioChunks([]);
      audioChunksRef.current = [];
      setIsRecording(true);
      addLog('system', 'Started recording audio');
      console.log('Recording started');

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          console.log('Audio data available:', event.data.size, 'bytes');
          // Update both state and ref
          setAudioChunks(prev => {
            const newChunks = [...prev, event.data];
            audioChunksRef.current = newChunks;
            return newChunks;
          });
        } else {
          console.log('Audio data available but size is 0');
        }
      };

      mediaRecorder.onstop = () => {
        setIsRecording(false);
        addLog('system', 'Stopped recording audio');
        console.log('Recording stopped');
        
        // Small delay to ensure all chunks are processed
        setTimeout(() => {
          // Use ref for immediate access to current chunks
          const currentChunks = audioChunksRef.current;
          console.log('Processing chunks from ref:', currentChunks.length, 'chunks');
          
          if (currentChunks.length > 0 && !isSendingAudio) {
            console.log('Creating audio blob from', currentChunks.length, 'chunks');
            const audioBlob = new Blob(currentChunks, { type: 'audio/webm' });
            sendAudioMessage(audioBlob);
          } else if (currentChunks.length === 0) {
            console.log('No audio chunks available');
            addLog('error', 'No audio data recorded');
          } else if (isSendingAudio) {
            console.log('Already sending audio, skipping duplicate');
          }
        }, 100); // Small delay to ensure chunks are processed
      };

      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        addLog('error', `MediaRecorder error: ${  'Unknown error'}`);
        setIsRecording(false);
      };

      mediaRecorder.start(1000); // Start recording with 1-second timeslices
      console.log('MediaRecorder started successfully');
    } catch (error) {
      console.error('Error starting MediaRecorder:', error);
      addLog('error', `Failed to start recording: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsRecording(false);
    }
  };

  const stopRecording = () => {
    if (!mediaRecorder) {
      console.log('No MediaRecorder available');
      addLog('error', 'No recording device available');
      return;
    }
    
    if (!isRecording) {
      console.log('Not currently recording');
      addLog('error', 'Not currently recording');
      return;
    }

    try {
      console.log('Stopping recording...');
      console.log('Current chunks before stop:', audioChunksRef.current.length);
      mediaRecorder.stop();
    } catch (error) {
      console.error('Error stopping recording:', error);
      addLog('error', 'Failed to stop recording');
      setIsRecording(false);
    }
  };

  // Dedicated recording functions for each mode
  const startDialerRecording = () => {
    if (!mediaRecorder || !isConnected) {
      console.log('Cannot start dialer recording:', { mediaRecorder: !!mediaRecorder, isConnected });
      addLog('error', `Cannot start recording: MediaRecorder=${!!mediaRecorder}, Connected=${isConnected}`);
      return;
    }

    try {
      // Clear both state and ref
      setAudioChunks([]);
      audioChunksRef.current = [];
      setIsRecording(true);
      addLog('system', 'Started dialer recording');
      console.log('Dialer recording started');

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          console.log('Audio data available:', event.data.size, 'bytes');
          // Update both state and ref
          setAudioChunks(prev => {
            const newChunks = [...prev, event.data];
            audioChunksRef.current = newChunks;
            return newChunks;
          });
        } else {
          console.log('Audio data available but size is 0');
        }
      };

      mediaRecorder.onstop = () => {
        setIsRecording(false);
        addLog('system', 'Stopped dialer recording');
        console.log('Dialer recording stopped');
        
        // Small delay to ensure all chunks are processed
        setTimeout(() => {
          // Use ref for immediate access to current chunks
          const currentChunks = audioChunksRef.current;
          console.log('Processing dialer chunks from ref:', currentChunks.length, 'chunks');
          
          if (currentChunks.length > 0 && !isSendingAudio) {
            console.log('Creating audio blob from', currentChunks.length, 'chunks');
            const audioBlob = new Blob(currentChunks, { type: 'audio/webm' });
            sendAudioMessage(audioBlob, 'dialer');
          } else if (currentChunks.length === 0) {
            console.log('No audio chunks available');
            addLog('error', 'No audio data recorded');
          } else if (isSendingAudio) {
            console.log('Already sending audio, skipping duplicate');
          }
        }, 100); // Small delay to ensure chunks are processed
      };

      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        addLog('error', `MediaRecorder error: ${'Unknown error'}`);
        setIsRecording(false);
      };

      mediaRecorder.start(1000); // Start recording with 1-second timeslices
      console.log('Dialer MediaRecorder started successfully');
    } catch (error) {
      console.error('Error starting Dialer MediaRecorder:', error);
      addLog('error', `Failed to start recording: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsRecording(false);
    }
  };

  const startConversationRecording = () => {
    if (!mediaRecorder || !isConnected) {
      console.log('Cannot start conversation recording:', { mediaRecorder: !!mediaRecorder, isConnected });
      addLog('error', `Cannot start recording: MediaRecorder=${!!mediaRecorder}, Connected=${isConnected}`);
      return;
    }

    try {
      // Clear both state and ref
      setAudioChunks([]);
      audioChunksRef.current = [];
      setIsRecording(true);
      addLog('system', 'Started conversation recording');
      console.log('Conversation recording started');

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          console.log('Audio data available:', event.data.size, 'bytes');
          // Update both state and ref
          setAudioChunks(prev => {
            const newChunks = [...prev, event.data];
            audioChunksRef.current = newChunks;
            return newChunks;
          });
        } else {
          console.log('Audio data available but size is 0');
        }
      };

      mediaRecorder.onstop = () => {
        setIsRecording(false);
        addLog('system', 'Stopped conversation recording');
        console.log('Conversation recording stopped');
        
        // Small delay to ensure all chunks are processed
        setTimeout(() => {
          // Use ref for immediate access to current chunks
          const currentChunks = audioChunksRef.current;
          console.log('Processing conversation chunks from ref:', currentChunks.length, 'chunks');
          
          if (currentChunks.length > 0 && !isSendingAudio) {
            console.log('Creating audio blob from', currentChunks.length, 'chunks');
            const audioBlob = new Blob(currentChunks, { type: 'audio/webm' });
            sendAudioMessage(audioBlob, 'conversation');
          } else if (currentChunks.length === 0) {
            console.log('No audio chunks available');
            addLog('error', 'No audio data recorded');
          } else if (isSendingAudio) {
            console.log('Already sending audio, skipping duplicate');
          }
        }, 100); // Small delay to ensure chunks are processed
      };

      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        addLog('error', `MediaRecorder error: ${'Unknown error'}`);
        setIsRecording(false);
      };

      mediaRecorder.start(1000); // Start recording with 1-second timeslices
      console.log('Conversation MediaRecorder started successfully');
    } catch (error) {
      console.error('Error starting Conversation MediaRecorder:', error);
      addLog('error', `Failed to start recording: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsRecording(false);
    }
  };

  const sendAudioMessage = async (audioBlob: Blob, mode: 'dialer' | 'conversation' = 'conversation') => {
    if (isSendingAudio) {
      console.log('Already sending audio, skipping duplicate');
      return;
    }

    try {
      setIsSendingAudio(true);
      console.log('Sending audio message, blob size:', audioBlob.size, 'bytes');
      console.log('Audio blob type:', audioBlob.type);
      
      const arrayBuffer = await audioBlob.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const base64Audio = btoa(Array.from(uint8Array, byte => String.fromCharCode(byte)).join(''));
      
      const message = {
        type: 'audio',
        data: base64Audio,
        format: audioBlob.type || 'audio/webm',
        mode: mode // Add mode to distinguish between dialer and conversation
      };

      console.log('Sending audio message to server...');
      sendMessage(message);
      addLog('user', `🎤 Voice message sent (${mode} mode)`);
    } catch (error) {
      console.error('Error sending audio:', error);
      addLog('error', `Failed to send audio message: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSendingAudio(false);
    }
  };

  const sendTextMessage = () => {
    if (!textInput.trim() || !isConnected) return;

    const message = {
      type: 'text',
      data: textInput.trim()
    };

    sendMessage(message);
    addLog('user', `💬 "${textInput.trim()}"`);
    setTextInput('');
  };

  const sendInitSignal = () => {
    const message = {
      type: 'signal',
      data: '__INIT__'
    };

    sendMessage(message);
    addLog('system', 'Sent initialization signal');
  };

  // Note: Audio playback is now handled in ConversationLog component

  return (
    <div className="component-card">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2 className="component-title">🎤 Voice Bot Tester</h2>
        <CompactConnectionStatus />
      </div>
      
      {/* Debug Information */}
      {/* <div style={{ 
        background: 'rgba(255, 193, 7, 0.1)', 
        border: '1px solid rgba(255, 193, 7, 0.3)', 
        borderRadius: '8px', 
        padding: '10px', 
        marginBottom: '20px',
        fontSize: '0.9rem'
      }}>
        <strong>🔧 Debug Info:</strong>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '5px', marginTop: '8px' }}>
          <div>MediaRecorder: {mediaRecorder ? '✅ Available' : '❌ Not Available'}</div>
          <div>Recording: {isRecording ? '🔴 Recording' : '⏸️ Not Recording'}</div>
          <div>Sending Audio: {isSendingAudio ? '📤 Sending' : '⏸️ Not Sending'}</div>
          <div>Audio Chunks: {audioChunksRef.current.length}</div>
        </div>
      </div> */}

      {/* Text Input */}
      {/* <div style={{ marginBottom: '20px' }}>
        <h3 style={{ marginBottom: '15px', color: '#fff', fontSize: '1.1rem' }}>💬 Text Input</h3>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <input
            type="text"
            className="input-field"
            placeholder="Type your message here..."
            value={textInput}
            onChange={(e) => setTextInput(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && sendTextMessage()}
            disabled={!isConnected}
            style={{ flex: 1, margin: 0 }}
          />
          <button 
            className="btn btn-secondary" 
            onClick={sendTextMessage}
            disabled={!textInput.trim() || !isConnected}
            style={{ 
              padding: '12px 20px',
              borderRadius: '8px',
              whiteSpace: 'nowrap'
            }}
          >
            📤 Send
          </button>
        </div>
      </div> */}

      {/* Phase 1 Testing - Dialer Integration */}
      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ marginBottom: '15px', color: '#fff', fontSize: '1.1rem' }}>📞 Phase 1: Dialer Integration</h3>
        <div style={{ 
          background: 'rgba(0, 123, 255, 0.1)', 
          border: '1px solid rgba(0, 123, 255, 0.3)', 
          borderRadius: '12px', 
          padding: '15px'
        }}>
          <p style={{ marginBottom: '15px', fontSize: '0.9rem', color: '#ccc', fontStyle: 'italic' }}>
            Real Call Flow: AI Greeting → Customer Speaks → AI Analyzes Intent → AI Responds
          </p>
          
          {/* Call Flow Steps */}
          <div style={{ marginBottom: '15px' }}>
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '10px', 
              marginBottom: '10px',
              padding: '8px 12px',
              background: 'rgba(255, 255, 255, 0.05)',
              borderRadius: '8px'
            }}>
              <span style={{ fontSize: '1.2rem' }}>1️⃣</span>
              <span style={{ fontSize: '0.9rem' }}>Start AI Greeting (AI speaks first)</span>
              <button 
                className="btn btn-success" 
                onClick={() => {
                  const message = { type: 'signal', data: 'DIALER_GREETING' };
                  sendMessage(message);
                  addLog('system', '🎤 Step 1: AI greeting started');
                }}
                disabled={!isConnected}
                style={{ padding: '8px 12px', fontSize: '0.8rem', marginLeft: 'auto' }}
              >
                🎤 Start Greeting
              </button>
            </div>
            
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '10px', 
              marginBottom: '10px',
              padding: '8px 12px',
              background: 'rgba(255, 255, 255, 0.05)',
              borderRadius: '8px'
            }}>
              <span style={{ fontSize: '1.2rem' }}>2️⃣</span>
              <span style={{ fontSize: '0.9rem' }}>After AI finishes speaking, record your response</span>
              {!isRecording ? (
                <button 
                  className="btn btn-warning" 
                  onClick={startDialerRecording}
                  disabled={!isConnected || !mediaRecorder}
                  style={{ 
                    padding: '8px 12px', 
                    fontSize: '0.8rem',
                    marginLeft: 'auto'
                  }}
                >
                  🎙️ Record Response
                </button>
              ) : (
                <button 
                  className="btn btn-danger" 
                  onClick={stopRecording}
                  style={{ 
                    padding: '8px 12px', 
                    fontSize: '0.8rem',
                    marginLeft: 'auto'
                  }}
                >
                  ⏹️ Stop
                </button>
              )}
            </div>
            
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '10px',
              padding: '8px 12px',
              background: 'rgba(255, 255, 255, 0.05)',
              borderRadius: '8px'
            }}>
              <span style={{ fontSize: '1.2rem' }}>3️⃣</span>
              <span style={{ fontSize: '0.9rem' }}>AI analyzes your voice/text and responds automatically</span>
            </div>
          </div>
          
          {/* Quick Test Buttons */}
          <div style={{ 
            borderTop: '1px solid rgba(255, 255, 255, 0.2)', 
            paddingTop: '15px',
            marginTop: '15px'
          }}>
            <p style={{ fontSize: '0.8rem', color: '#ccc', marginBottom: '10px' }}>
              Quick Test (for demo purposes):
            </p>
            <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
              <button 
                className="btn btn-warning" 
                onClick={() => {
                  const message = { type: 'signal', data: 'CUSTOMER_YES' };
                  sendMessage(message);
                  addLog('user', '✅ Quick Test: Customer says "Yes"');
                }}
                disabled={!isConnected}
                style={{ padding: '6px 12px', fontSize: '0.8rem' }}
              >
                ✅ "Yes"
              </button>
              
              <button 
                className="btn btn-danger" 
                onClick={() => {
                  const message = { type: 'signal', data: 'CUSTOMER_NO' };
                  sendMessage(message);
                  addLog('user', '❌ Quick Test: Customer says "No"');
                }}
                disabled={!isConnected}
                style={{ padding: '6px 12px', fontSize: '0.8rem' }}
              >
                ❌ "No"
              </button>
            </div>
          </div>
        </div>
      </div>



      {/* Control Buttons */}
      {/* <div style={{ marginBottom: '20px' }}>
        <h3 style={{ marginBottom: '15px', color: '#fff', fontSize: '1.1rem' }}>🔧 General Controls</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', gap: '10px' }}>
          <button 
            className="btn btn-success" 
            onClick={sendInitSignal}
            disabled={!isConnected}
            style={{ padding: '10px 15px', fontSize: '0.9rem' }}
          >
            🚀 Init Signal
          </button>
          
          <button 
            className="btn btn-info" 
            onClick={() => {
              const message = { type: 'text', data: 'Test message from UI' };
              sendMessage(message);
              addLog('system', 'Sent test text message');
            }}
            disabled={!isConnected}
            style={{ padding: '10px 15px', fontSize: '0.9rem' }}
          >
            🧪 Test Text
          </button>
          
          <button 
            className="btn btn-secondary" 
            onClick={() => setLogs([])}
            style={{ padding: '10px 15px', fontSize: '0.9rem' }}
          >
            🗑️ Clear Logs
          </button>
        </div>
      </div> */}
    </div>
  );
};

export default VoiceBotTester; 