import React from 'react';
import { useWebSocket } from '../context/WebSocketContext';

const ConnectionStatus: React.FC = () => {
  const { isConnected, isConnecting, connect, disconnect, connectionError } = useWebSocket();

  const getStatusText = () => {
    if (isConnecting) return 'Connecting...';
    if (isConnected) return 'Connected';
    return 'Disconnected';
  };

  const getStatusClass = () => {
    if (isConnecting) return 'status-connecting';
    if (isConnected) return 'status-connected';
    return 'status-disconnected';
  };

  const getStatusIcon = () => {
    if (isConnecting) return '⏳';
    if (isConnected) return '✅';
    return '❌';
  };

  return (
    <div className="component-card">
      <h2 className="component-title">🔌 Connection Status</h2>
      
      <div style={{ marginBottom: '15px' }}>
        <span className={`status-indicator ${getStatusClass()}`}>
          {getStatusIcon()} {getStatusText()}
        </span>
      </div>

      {connectionError && (
        <div style={{ 
          background: 'rgba(255, 107, 107, 0.1)', 
          color: '#ff6b6b', 
          padding: '10px', 
          borderRadius: '8px', 
          marginBottom: '15px',
          border: '1px solid rgba(255, 107, 107, 0.3)'
        }}>
          <strong>Error:</strong> {connectionError}
        </div>
      )}

      <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
        {!isConnected && !isConnecting && (
          <button className="btn btn-primary" onClick={connect}>
            🔗 Connect
          </button>
        )}
        
        {isConnected && (
          <button className="btn btn-danger" onClick={disconnect}>
            🔌 Disconnect
          </button>
        )}
        
        {isConnecting && (
          <button className="btn btn-secondary" disabled>
            ⏳ Connecting...
          </button>
        )}
      </div>

      <div style={{ marginTop: '15px', fontSize: '0.9rem', opacity: 0.8 }}>
        <strong>Server:</strong> ws://localhost:8765
      </div>
    </div>
  );
};

export default ConnectionStatus; 