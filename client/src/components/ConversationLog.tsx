import React, { useEffect, useRef } from 'react';
import { useWebSocket } from '../context/WebSocketContext';

interface LogEntry {
  id: string;
  timestamp: string;
  type: 'user' | 'bot' | 'system' | 'error';
  content: string;
}

const ConversationLog: React.FC = () => {
  const { lastMessage, sentMessage, audioState, playAudio } = useWebSocket();
  const [logs, setLogs] = React.useState<LogEntry[]>([]);
  const [lastAudioMessageId, setLastAudioMessageId] = React.useState<string>('');
  const logEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    logEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [logs]);

  // Handle incoming messages from WebSocket
  useEffect(() => {
    if (lastMessage) {
      const timestamp = new Date().toLocaleTimeString();
      
      if (lastMessage.type === 'audio' && lastMessage.data) {
        // Create a unique ID for this audio message to prevent duplicates
        const audioMessageId = `${lastMessage.data.length}_${Date.now()}`;
        
        // Check if this is a duplicate of the last audio message
        if (audioMessageId === lastAudioMessageId) {
          console.log('🎵 Duplicate audio message detected, skipping playback');
          return;
        }
        
        setLastAudioMessageId(audioMessageId);
        
        // Show user's transcribed speech first
        if (lastMessage.user_transcript) {
          const userLogEntry: LogEntry = {
            id: (Date.now() - 1).toString(), // Slightly earlier timestamp
            timestamp,
            type: 'user',
            content: `👤 "${lastMessage.user_transcript}"`,
          };
          setLogs(prev => [...prev, userLogEntry]);
        }
        
        // Show the actual AI response text
        const aiResponseText = lastMessage.text || 'AI Response';
        const logEntry: LogEntry = {
          id: Date.now().toString(),
          timestamp,
          type: 'bot',
          content: `🤖 "${aiResponseText}"`,
        };
        setLogs(prev => [...prev, logEntry]);
        
        // Play the audio response using global audio management
        playAudio(lastMessage.data).catch(error => {
          console.error('❌ Error playing audio:', error);
        });
      } else if (lastMessage.text) {
        const logEntry: LogEntry = {
          id: Date.now().toString(),
          timestamp,
          type: 'bot',
          content: `🤖 "${lastMessage.text}"`,
        };
        setLogs(prev => [...prev, logEntry]);
      } else if (lastMessage.type === 'signal') {
        // Handle different signal types
        let signalContent = '';
        switch (lastMessage.data) {
          case 'DIALER_GREETING':
            signalContent = '🎤 AI Greeting Started';
            break;
          case 'CUSTOMER_YES':
            signalContent = '✅ Customer: "Yes"';
            break;
          case 'CUSTOMER_NO':
            signalContent = '❌ Customer: "No"';
            break;
          case 'NORMAL_CONVERSATION':
            signalContent = '💬 Normal Conversation Mode';
            break;
          default:
            signalContent = `📡 Signal: ${lastMessage.data}`;
        }
        
        const logEntry: LogEntry = {
          id: Date.now().toString(),
          timestamp,
          type: 'system',
          content: signalContent,
        };
        setLogs(prev => [...prev, logEntry]);
      } else {
        const logEntry: LogEntry = {
          id: Date.now().toString(),
          timestamp,
          type: 'system',
          content: `📨 ${JSON.stringify(lastMessage)}`,
        };
        setLogs(prev => [...prev, logEntry]);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lastMessage]);

  // Handle sent messages (user messages)
  useEffect(() => {
    if (sentMessage) {
      const timestamp = new Date().toLocaleTimeString();
      
      if (sentMessage.type === 'audio') {
        const logEntry: LogEntry = {
          id: Date.now().toString(),
          timestamp,
          type: 'user',
          content: `🎤 Voice message sent`,
        };
        setLogs(prev => [...prev, logEntry]);
      } else if (sentMessage.type === 'text') {
        const logEntry: LogEntry = {
          id: Date.now().toString(),
          timestamp,
          type: 'user',
          content: `👤 "${sentMessage.data}"`,
        };
        setLogs(prev => [...prev, logEntry]);
      } else if (sentMessage.type === 'signal') {
        // Handle signal messages sent by user
        let signalContent = '';
        switch (sentMessage.data) {
          case 'DIALER_GREETING':
            signalContent = '🎤 Started AI Greeting';
            break;
          case 'CUSTOMER_YES':
            signalContent = '✅ Simulated: "Yes"';
            break;
          case 'CUSTOMER_NO':
            signalContent = '❌ Simulated: "No"';
            break;
          case 'NORMAL_CONVERSATION':
            signalContent = '💬 Started Normal Conversation';
            break;
          default:
            signalContent = `📡 Signal: ${sentMessage.data}`;
        }
        
        const logEntry: LogEntry = {
          id: Date.now().toString(),
          timestamp,
          type: 'system',
          content: signalContent,
        };
        setLogs(prev => [...prev, logEntry]);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sentMessage]);





  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'user': return '👤';
      case 'bot': return '🤖';
      case 'system': return '⚙️';
      case 'error': return '❌';
      default: return '📝';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'user': return 'User';
      case 'bot': return 'Bot';
      case 'system': return 'System';
      case 'error': return 'Error';
      default: return 'Info';
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="component-card">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
        <h2 className="component-title">📋 Conversation Log</h2>
        <button className="btn btn-secondary" onClick={clearLogs}>
          🗑️ Clear
        </button>
      </div>

      <div className="conversation-log">
        {logs.length === 0 ? (
          <div style={{ 
            textAlign: 'center', 
            padding: '40px', 
            opacity: 0.6,
            fontStyle: 'italic'
          }}>
            No conversation logs yet. Start testing to see messages here.
          </div>
        ) : (
          logs.map((log) => (
            <div key={log.id} className={`log-entry ${log.type}`}>
              <div className="log-timestamp">
                {getTypeIcon(log.type)} {getTypeLabel(log.type)} • {log.timestamp}
              </div>
              <div className="log-content">
                {log.content}
              </div>
            </div>
          ))
        )}
        <div ref={logEndRef} />
      </div>

      <div style={{ 
        marginTop: '15px', 
        fontSize: '0.9rem', 
        opacity: 0.7,
        textAlign: 'center'
      }}>
        {logs.length} message{logs.length !== 1 ? 's' : ''} in conversation
        {audioState.isPlaying && (
          <div style={{
            marginTop: '5px',
            color: '#4CAF50',
            fontWeight: 'bold'
          }}>
            🔊 Playing audio response...
          </div>
        )}
      </div>
    </div>
  );
};

export default ConversationLog; 