import React, { useState, useEffect, useRef } from 'react';
import { useWebSocket } from '../context/WebSocketContext';

const NormalConversationSection: React.FC = () => {
  const { sendMessage, isConnected } = useWebSocket();
  const [isRecording, setIsRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [audioChunks, setAudioChunks] = useState<Blob[]>([]);
  const [isSendingAudio, setIsSendingAudio] = useState(false);
  const audioChunksRef = useRef<Blob[]>([]);

  const addLog = (type: 'user' | 'bot' | 'system' | 'error', content: string) => {
    // This will be handled by the conversation log component
    console.log(`${type}: ${content}`);
  };

  // Initialize MediaRecorder
  useEffect(() => {
    const initAudio = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        
        // Try different MIME types
        const mimeTypes = [
          'audio/webm;codecs=opus',
          'audio/webm',
          'audio/mp4',
          'audio/ogg;codecs=opus',
          'audio/wav'
        ];
        
        let selectedType = '';
        for (const type of mimeTypes) {
          if (MediaRecorder.isTypeSupported(type)) {
            selectedType = type;
            break;
          }
        }
        
        if (!selectedType) {
          console.error('No supported audio MIME type found');
          return;
        }
        
        const recorder = new MediaRecorder(stream, { mimeType: selectedType });
        setMediaRecorder(recorder);
        console.log('MediaRecorder initialized with type:', selectedType);
      } catch (error) {
        console.error('Error accessing microphone:', error);
      }
    };
    
    initAudio();
  }, []);

  const startConversationRecording = () => {
    if (!mediaRecorder || !isConnected) {
      console.log('Cannot start conversation recording:', { mediaRecorder: !!mediaRecorder, isConnected });
      return;
    }

    try {
      // Clear both state and ref
      setAudioChunks([]);
      audioChunksRef.current = [];
      setIsRecording(true);
      console.log('Conversation recording started');

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          console.log('Audio data available:', event.data.size, 'bytes');
          // Update both state and ref
          setAudioChunks(prev => {
            const newChunks = [...prev, event.data];
            audioChunksRef.current = newChunks;
            return newChunks;
          });
        }
      };

      mediaRecorder.onstop = () => {
        setIsRecording(false);
        console.log('Conversation recording stopped');
        
        // Small delay to ensure all chunks are processed
        setTimeout(() => {
          // Use ref for immediate access to current chunks
          const currentChunks = audioChunksRef.current;
          console.log('Processing conversation chunks from ref:', currentChunks.length, 'chunks');
          
          if (currentChunks.length > 0 && !isSendingAudio) {
            console.log('Creating audio blob from', currentChunks.length, 'chunks');
            const audioBlob = new Blob(currentChunks, { type: 'audio/webm' });
            sendConversationAudio(audioBlob);
          } else if (currentChunks.length === 0) {
            console.log('No audio chunks available');
          } else if (isSendingAudio) {
            console.log('Already sending audio, skipping duplicate');
          }
        }, 100); // Small delay to ensure chunks are processed
      };

      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        setIsRecording(false);
      };

      mediaRecorder.start(1000); // Start recording with 1-second timeslices
      console.log('Conversation MediaRecorder started successfully');
    } catch (error) {
      console.error('Error starting Conversation MediaRecorder:', error);
      setIsRecording(false);
    }
  };

  const stopRecording = () => {
    if (!mediaRecorder) {
      console.log('No MediaRecorder available');
      return;
    }
    
    if (!isRecording) {
      console.log('Not currently recording');
      return;
    }

    try {
      console.log('Stopping recording...');
      mediaRecorder.stop();
    } catch (error) {
      console.error('Error stopping recording:', error);
      setIsRecording(false);
    }
  };

  const sendConversationAudio = async (audioBlob: Blob) => {
    if (isSendingAudio) {
      console.log('Already sending audio, skipping duplicate');
      return;
    }

    try {
      setIsSendingAudio(true);
      console.log('Sending conversation audio, blob size:', audioBlob.size, 'bytes');
      
      const arrayBuffer = await audioBlob.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const base64Audio = btoa(Array.from(uint8Array, byte => String.fromCharCode(byte)).join(''));
      
      const message = {
        type: 'audio',
        data: base64Audio,
        format: audioBlob.type || 'audio/webm',
        mode: 'conversation'
      };

      console.log('Sending conversation audio to server...');
      sendMessage(message);
    } catch (error) {
      console.error('Error sending conversation audio:', error);
    } finally {
      setIsSendingAudio(false);
    }
  };

  return (
    <div className="component-card">
      <h2 className="component-title">💬 Phase 1: Normal Conversation</h2>
      <div style={{ 
        background: 'rgba(40, 167, 69, 0.1)', 
        border: '1px solid rgba(40, 167, 69, 0.3)', 
        borderRadius: '12px', 
        padding: '15px'
      }}>
        <p style={{ marginBottom: '15px', fontSize: '0.9rem', color: '#ccc', fontStyle: 'italic' }}>
          Start conversation → Record message → AI responds → Record message → continues...
        </p>
        
        {/* Conversation Flow */}
        <div style={{ marginBottom: '15px' }}>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '10px', 
            marginBottom: '10px',
            padding: '8px 12px',
            background: 'rgba(255, 255, 255, 0.05)',
            borderRadius: '8px'
          }}>
            <span style={{ fontSize: '1.2rem' }}>1️⃣</span>
            <span style={{ fontSize: '0.9rem' }}>Start conversation mode</span>
            <button 
              className="btn btn-primary" 
              onClick={() => {
                const message = { type: 'signal', data: 'NORMAL_CONVERSATION' };
                sendMessage(message);
                addLog('system', '💬 Started Normal conversation mode');
              }}
              disabled={!isConnected}
              style={{ 
                padding: '8px 12px', 
                fontSize: '0.8rem',
                marginLeft: 'auto'
              }}
            >
              💬 Start Conversation
            </button>
          </div>
          
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '10px',
            padding: '8px 12px',
            background: 'rgba(255, 255, 255, 0.05)',
            borderRadius: '8px'
          }}>
            <span style={{ fontSize: '1.2rem' }}>2️⃣</span>
            <span style={{ fontSize: '0.9rem' }}>Record your message</span>
            {!isRecording ? (
              <button 
                className="btn btn-success" 
                onClick={startConversationRecording}
                disabled={!isConnected || !mediaRecorder}
                style={{ 
                  padding: '8px 12px', 
                  fontSize: '0.8rem',
                  marginLeft: 'auto'
                }}
              >
                🎙️ Record Message
              </button>
            ) : (
              <button 
                className="btn btn-danger" 
                onClick={stopRecording}
                style={{ 
                  padding: '8px 12px', 
                  fontSize: '0.8rem',
                  marginLeft: 'auto'
                }}
              >
                ⏹️ Stop
              </button>
            )}
          </div>
        </div>
        
        {/* Quick Test Buttons */}
        <div style={{ 
          borderTop: '1px solid rgba(255, 255, 255, 0.2)', 
          paddingTop: '15px',
          marginTop: '15px'
        }}>
          <p style={{ fontSize: '0.8rem', color: '#ccc', marginBottom: '10px' }}>
            Quick Test (text messages):
          </p>
          <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
            <button 
              className="btn btn-info" 
              onClick={() => {
                const message = { type: 'text', data: 'I want health insurance' };
                sendMessage(message);
                addLog('user', '💬 Customer: "I want health insurance"');
              }}
              disabled={!isConnected}
              style={{ padding: '6px 12px', fontSize: '0.8rem' }}
            >
              🏥 Health Insurance
            </button>
            
            <button 
              className="btn btn-info" 
              onClick={() => {
                const message = { type: 'text', data: 'I need car insurance' };
                sendMessage(message);
                addLog('user', '🚗 Car Insurance');
              }}
              disabled={!isConnected}
              style={{ padding: '6px 12px', fontSize: '0.8rem' }}
            >
              🚗 Car Insurance
            </button>
            
            <button 
              className="btn btn-info" 
              onClick={() => {
                const message = { type: 'text', data: 'Tell me about life insurance' };
                sendMessage(message);
                addLog('user', '🛡️ Life Insurance');
              }}
              disabled={!isConnected}
              style={{ padding: '6px 12px', fontSize: '0.8rem' }}
            >
              🛡️ Life Insurance
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NormalConversationSection; 