"""
WebSocket server service for voice bot
"""

import asyncio
import logging
import time
from typing import Dict, Any

import websockets
from websockets.server import WebSocketServerProtocol

from config import SERVER_CONFIG

logger = logging.getLogger(__name__)


class WebSocketServer:
    """
    Professional WebSocket server for voice bot
    
    Features:
    - Connection management
    - Session tracking
    - Status monitoring
    - Error handling
    """
    
    def __init__(self, voice_bot):
        """Initialize WebSocket server"""
        self.voice_bot = voice_bot
        self.active_connections = 0
        self.total_connections = 0
        self.server = None
        logger.info("🌐 WebSocket server initialized")
    
    async def start_server(self):
        """Start the WebSocket server"""
        try:
            logger.info("🔧 Starting WebSocket server...")
            
            # Start WebSocket server
            self.server = await websockets.serve(
                self._handle_websocket,
                SERVER_CONFIG["websocket"]["host"],
                SERVER_CONFIG["websocket"]["port"],
                ping_interval=SERVER_CONFIG["websocket"]["ping_interval"],
                ping_timeout=SERVER_CONFIG["websocket"]["ping_timeout"]
            )
            
            logger.info("🌐 Voice Bot WebSocket server started successfully!")
            logger.info(f"📍 Server address: ws://{SERVER_CONFIG['websocket']['host']}:{SERVER_CONFIG['websocket']['port']}")
            logger.info(f"⚙️ Ping interval: {SERVER_CONFIG['websocket']['ping_interval']}s")
            logger.info(f"⏱️ Ping timeout: {SERVER_CONFIG['websocket']['ping_timeout']}s")
            logger.info("🎤 Ready to receive audio chunks from dialer team...")
            logger.info("📈 Server is now listening for connections...")
            
            # Start status logging
            asyncio.create_task(self._log_server_status())
            
            # Keep server running
            await self.server.wait_closed()
            
        except Exception as e:
            logger.error(f"❌ Failed to start server: {e}")
            raise
    
    async def _handle_websocket(self, websocket: WebSocketServerProtocol, path: str):
        """Main WebSocket handler for all connections"""
        session_id = f"session_{id(websocket)}"
        
        try:
            self.active_connections += 1
            self.total_connections += 1
            logger.info(f"🔌 New WebSocket connection: {session_id} from {websocket.remote_address}")
            logger.info(f"📊 WebSocket path: {path}")
            
            # Initialize session
            self.voice_bot.conversation_context[session_id] = []
            self.voice_bot._session_start_times[session_id] = time.time()
            
            # Log session start
            client_info = {
                "remote_address": str(websocket.remote_address),
                "path": path,
                "user_agent": getattr(websocket, 'request_headers', {}).get('User-Agent', 'Unknown')
            }
            self.voice_bot.conversation_logger.log_session_start(session_id, "normal_conversation", client_info)
            
            logger.info(f"🔌 Handling connection for {session_id}")
            
            async for message in websocket:
                try:
                    # Parse message
                    data = self.voice_bot._parse_message(message, session_id)
                    if data is None:
                        continue
                    
                    # Handle different message types
                    await self.voice_bot._route_message(websocket, session_id, data)
                    
                except Exception as e:
                    logger.error(f"❌ Error handling message: {e}")
                    self.voice_bot.conversation_logger.log_error(session_id, "message_handling_error", str(e))
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"📞 Connection closed: {session_id}")
        except Exception as e:
            logger.error(f"❌ Fatal error in connection: {e}")
            self.voice_bot.conversation_logger.log_error(session_id, "fatal_connection_error", str(e))
        finally:
            # Cleanup
            self.active_connections -= 1
            
            # Log session end
            if session_id in self.voice_bot._session_start_times:
                total_duration = time.time() - self.voice_bot._session_start_times[session_id]
                conversation_summary = {
                    "total_messages": len(self.voice_bot.conversation_context.get(session_id, [])),
                    "session_mode": self.voice_bot._session_modes.get(session_id, "unknown"),
                    "greeting_sent": self.voice_bot.session_utils.is_greeting_sent(session_id)
                }
                self.voice_bot.conversation_logger.log_session_end(session_id, "connection_closed", total_duration, conversation_summary)
                del self.voice_bot._session_start_times[session_id]
            
            if session_id in self.voice_bot.conversation_context:
                del self.voice_bot.conversation_context[session_id]
            logger.info(f"🧹 Cleaned up session: {session_id}")
    
    async def _log_server_status(self):
        """Log server status periodically"""
        while True:
            try:
                await asyncio.sleep(30)  # Log every 30 seconds
                logger.info(f"📊 Server Status - Active connections: {self.active_connections}, Total connections: {self.total_connections}")
            except Exception as e:
                logger.error(f"❌ Error in status logging: {e}")
    
    def get_server_stats(self) -> Dict[str, Any]:
        """Get server statistics"""
        return {
            "active_connections": self.active_connections,
            "total_connections": self.total_connections,
            "server_running": self.server is not None
        } 