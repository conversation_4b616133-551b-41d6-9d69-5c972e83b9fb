"""
Intent analyzer service for Phase 1 simple intent detection
"""

import logging
from typing import Dict, Any, Optional

from .gemini_service import GeminiService

logger = logging.getLogger(__name__)


class IntentAnalyzer:
    """
    Intent analyzer for Phase 1 simple intent detection
    
    Features:
    - Bucket responses for common scenarios
    - AI-powered responses for complex scenarios
    - Early exit keyword detection
    - Hybrid approach for efficiency
    """
    
    def __init__(self, gemini_service: GeminiService):
        """Initialize intent analyzer with Gemini service"""
        self.gemini_service = gemini_service
        self._initialize_bucket_responses()
        logger.info("✅ Intent analyzer initialized")
    
    def _initialize_bucket_responses(self):
        """Initialize bucket responses for common scenarios"""
        self.bucket_responses = {
            "greeting": {
                "message": "Hello! This is Policybazaar, India's leading insurance marketplace. We noticed you recently showed interest in our insurance services. Would you like to speak to an insurance advisor for assistance?",
                "action": "continue",
                "reason": "greeting"
            },
            "interested": {
                "message": "Great! Connecting you to a certified insurance advisor who can assist you further. Please hold the line.",
                "action": "end_call",
                "reason": "connecting_to_agent"
            },
            "not_interested": {
                "message": "No problem, we will send you a link on WhatsApp where you can explore our insurance options. When you open the link, there will be a call button available for you to reach us anytime. Thank you for your time, have a great day!",
                "action": "end_call",
                "reason": "customer_not_interested"
            },
            "unclear": {
                "message": "Sorry, I didn't catch that clearly. Are you looking for assistance with your insurance needs today?",
                "action": "continue",
                "reason": "unclear_response"
            }
        }
        logger.info("✅ Bucket responses initialized")
    
    async def analyze_customer_response(self, transcript: str, session_id: str = None) -> Dict[str, Any]:
        """
        Natural conversation flow: AI responds to each message naturally,
        only connects to advisor or disconnects when customer shows clear intent
        
        Args:
            transcript: Customer's transcribed speech
            session_id: Session ID for logging
            
        Returns:
            Response dictionary with message, action, and reason
        """
        try:
            logger.info(f"🤖 Analyzing customer response: '{transcript}'")
            
            # Use AI to analyze intent and generate appropriate response
            logger.info(f"💬 AI-powered conversation analysis: '{transcript}'")
            ai_response = await self.gemini_service.generate_response(transcript)
            
            if ai_response:
                logger.info(f"✅ AI generated natural response: '{ai_response}'")
                return {
                    "message": ai_response,
                    "action": "continue",
                    "reason": "natural_conversation"
                }
            else:
                logger.warning(f"⚠️ AI failed to generate response, using fallback: '{transcript}'")
                return self.bucket_responses["unclear"]
                
        except Exception as e:
            logger.error(f"❌ Error analyzing customer response: {e}")
            # Fallback to bucket response if AI fails
            return self.bucket_responses["unclear"]
    
    def get_greeting_response(self) -> Dict[str, Any]:
        """Get the greeting response for new calls"""
        return self.bucket_responses["greeting"]
    
    def get_response_by_reason(self, reason: str) -> Optional[Dict[str, Any]]:
        """Get response by reason"""
        return self.bucket_responses.get(reason)
    
    def analyze_intent(self, response: str) -> Dict[str, Any]:
        """
        Analyze simple intent for Phase 1 testing
        
        Args:
            response: Simple response like "yes", "no", etc.
            
        Returns:
            Response dictionary with message, action, and reason
        """
        try:
            normalized_response = response.lower().strip()
            
            if normalized_response in ["yes", "yeah", "sure", "okay", "alright", "interested"]:
                logger.info(f"✅ Customer interested: '{response}'")
                return self.bucket_responses["interested"]
                
            elif normalized_response in ["no", "nope", "not interested", "not now"]:
                logger.info(f"❌ Customer not interested: '{response}'")
                return self.bucket_responses["not_interested"]
                
            else:
                logger.info(f"❓ Unclear response: '{response}'")
                return self.bucket_responses["unclear"]
                
        except Exception as e:
            logger.error(f"❌ Error analyzing intent: {e}")
            return self.bucket_responses["unclear"] 