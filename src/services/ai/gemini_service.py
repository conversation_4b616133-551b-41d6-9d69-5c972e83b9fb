"""
Gemini AI service for Vertex AI interactions
"""

import logging
import re
import time
from typing import Optional, Dict, Any, List

from google.cloud import aiplatform
from vertexai.generative_models import GenerativeModel

from config import AI_CONFIG, SYSTEM_PROMPT, ERROR_MESSAGES

logger = logging.getLogger(__name__)


class GeminiService:
    """
    Professional Gemini AI service for voice bot interactions
    
    Features:
    - Vertex AI integration
    - Conversation management
    - Response generation
    - Error handling and fallbacks
    """
    
    def __init__(self):
        """Initialize Gemini AI service"""
        try:
            logger.info("🤖 Initializing Gemini AI service...")
            
            # Initialize Vertex AI
            aiplatform.init(project=AI_CONFIG["project"])
            logger.info("✅ Vertex AI initialized")
            
            # Initialize the model
            self.model = GenerativeModel(AI_CONFIG["model"])
            logger.info(f"✅ Gemini model initialized: {AI_CONFIG['model']}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Gemini AI service: {e}")
            raise
    
    async def generate_response(self, user_input: str, conversation_history: List[Dict[str, str]] = None) -> Optional[str]:
        """
        Generate AI response using Vertex AI Gemini model
        
        Args:
            user_input: The user's input text
            conversation_history: List of previous conversation messages
            
        Returns:
            Generated AI response or None if failed
        """
        try:
            logger.info(f"🤖 Generating AI response for: '{user_input}'")
            
            # Build conversation context
            messages = []
            
            # Add system prompt and user input
            prompt = f"{SYSTEM_PROMPT}\n\nUser: {user_input}"
            
            # Add conversation history if available
            if conversation_history:
                # Add last few messages for context (respecting max_history)
                recent_history = conversation_history[-AI_CONFIG["context_messages"]:]
                context = "\n".join([f"{msg['role']}: {msg['content']}" for msg in recent_history])
                prompt = f"{SYSTEM_PROMPT}\n\nConversation History:\n{context}\n\nUser: {user_input}"
            
            # Generate response using Vertex AI
            logger.info("🤖 Calling Vertex AI Gemini model...")
            ai_start = time.time()
            
            response = self.model.generate_content(
                prompt,
                generation_config={
                    "temperature": AI_CONFIG["temperature"],
                    "max_output_tokens": AI_CONFIG["max_output_tokens"],
                    "top_p": AI_CONFIG["top_p"]
                }
            )
            
            ai_time = time.time() - ai_start
            logger.info(f"🕒 AI generation time: {ai_time:.2f}s")
            
            ai_response = response.text
            
            # Clean the response text for TTS (remove markdown formatting)
            ai_response = self._clean_ai_response(ai_response)
            
            logger.info(f"✅ AI Response from Vertex AI: '{ai_response}'")
            return ai_response
            
        except Exception as e:
            logger.error(f"❌ Error generating AI response: {e}")
            # Return fallback response
            return self._generate_fallback_response(user_input)
    
    async def analyze_intent(self, transcript: str) -> str:
        """
        Analyze customer intent using Vertex AI
        
        Args:
            transcript: Customer's transcribed speech
            
        Returns:
            Intent classification: INTERESTED, NOT_INTERESTED, or UNCLEAR
        """
        try:
            logger.info(f"🤖 Analyzing customer intent: '{transcript}'")
            
            # Create a comprehensive prompt for intent analysis that handles multilingual responses
            intent_prompt = f"""Analyze the customer's response and classify their intent.

Customer response: "{transcript}"

Instructions:
- INTERESTED: Customer wants to speak to an advisor, asks for details, says yes/okay/alright, or shows interest
- NOT_INTERESTED: Customer says no, not interested, wrong number, busy, call later, or wants to end the call
- UNCLEAR: Unclear response, greeting only, or neutral response

Common patterns:
- Hindi/Urdu responses like "mujhe kal call karna" (call me tomorrow) = NOT_INTERESTED
- "hello", "hi" = UNCLEAR (need more context)
- "yes", "okay", "sure" = INTERESTED
- "no", "not interested", "busy" = NOT_INTERESTED

Classify as:"""

            # Get AI analysis with increased token limit
            ai_start = time.time()
            response = self.model.generate_content(
                intent_prompt,
                generation_config={
                    "temperature": 0.0,  # Zero temperature for consistent classification
                    "max_output_tokens": 50,  # Increased from 5 to 50
                    "top_p": 0.1
                }
            )
            ai_time = time.time() - ai_start
            logger.info(f"🕒 Intent analysis time: {ai_time:.2f}s")
            
            ai_intent = response.text.strip().upper()
            logger.info(f"🤖 Intent analysis result: '{ai_intent}' for '{transcript}'")
            
            # Clean and validate the response
            if "INTERESTED" in ai_intent:
                return "INTERESTED"
            elif "NOT_INTERESTED" in ai_intent:
                return "NOT_INTERESTED"
            else:
                return "UNCLEAR"
            
        except Exception as e:
            logger.error(f"❌ Error analyzing intent with Vertex AI: {e}")
            # Fallback to enhanced keyword matching
            return self._fallback_intent_analysis(transcript)
    
    def _clean_ai_response(self, ai_response: str) -> str:
        """Clean AI response for TTS (remove markdown formatting)"""
        # Remove markdown bold formatting (**text**)
        ai_response = re.sub(r'\*\*(.*?)\*\*', r'\1', ai_response)
        # Remove markdown italic formatting (*text*)
        ai_response = re.sub(r'\*(.*?)\*', r'\1', ai_response)
        # Remove any remaining markdown symbols
        ai_response = re.sub(r'#+\s*', '', ai_response)  # Remove headers
        ai_response = re.sub(r'`(.*?)`', r'\1', ai_response)  # Remove code blocks
        ai_response = re.sub(r'\[(.*?)\]\(.*?\)', r'\1', ai_response)  # Remove links
        # Clean up extra whitespace
        ai_response = re.sub(r'\n\s*\n', '\n', ai_response)  # Remove extra line breaks
        return ai_response.strip()

    def _generate_fallback_response(self, user_input: str) -> str:
        """Generate fallback response based on user input"""
        if "health insurance" in user_input.lower():
            return "I understand you're looking for health insurance. I can help you find the best health insurance plans based on your needs. Could you tell me more about your requirements, such as your age, family size, and any specific medical conditions?"
        elif "car insurance" in user_input.lower() or "motor insurance" in user_input.lower():
            return "I can help you with motor insurance for your car or bike. Please let me know the vehicle details, your driving history, and any specific coverage requirements you have."
        elif "life insurance" in user_input.lower():
            return "I can assist you with life insurance options. To provide the best recommendations, I need to know your age, income, family responsibilities, and any existing insurance coverage."
        elif "travel insurance" in user_input.lower():
            return "I can help you find suitable travel insurance. Please tell me about your travel destination, duration, and any specific activities you'll be doing during your trip."
        else:
            return f"Thank you for your message: '{user_input}'. I'm here to help you with all your insurance needs including health, motor, life, and travel insurance. How can I assist you today?"

    def _fallback_intent_analysis(self, transcript: str) -> str:
        """Fallback intent analysis using enhanced keyword matching"""
        normalized_input = transcript.lower().strip()
        
        # Enhanced keywords for not interested (including Hindi/Urdu)
        not_interested_keywords = [
            # English
            "no", "not interested", "not now", "don't call", "wrong number",
            "busy", "not available", "call later", "call me later", "not right time",
            "not looking", "not needed", "not required", "end call",
            "hang up", "disconnect", "bye", "goodbye",
            # Hindi/Urdu patterns
            "nahi", "nhi", "na", "mat karo", "call mat karo",
            "kal", "parso", "baad mein", "time nahi", "busy hun",
            "galat number", "wrong number", "call later", "baad mein call karo",
            "mujhe kal", "mujhe parso", "time nahi hai", "busy hun",
            "5 baje", "6 baje", "7 baje", "8 baje", "9 baje", "10 baje",
            "sham ko", "raat ko", "subah", "dopahar", "shaam"
        ]
        
        # Enhanced keywords for interested
        interested_keywords = [
            # English
            "yes", "interested", "sure", "okay", "alright", "tell me more",
            "what do you have", "show me", "explain", "details", "connect",
            "advisor", "speak to", "talk to", "help me", "assist",
            # Hindi/Urdu patterns
            "ha", "haan", "ji", "bilkul", "theek hai", "sahi hai",
            "batayein", "batao", "details", "information", "help",
            "advisor se", "expert se", "speak karna", "talk karna",
            "insurance", "policy", "plan", "quote", "premium"
        ]
        
        # Check for not interested keywords (including Hindi/Urdu patterns)
        for keyword in not_interested_keywords:
            if keyword in normalized_input:
                # Special handling to avoid false positives
                if keyword == "na" and any(word in normalized_input for word in ["karna", "baat karna", "speak karna", "talk karna"]):
                    continue  # Skip if "na" is part of "karna" (speak/talk)
                if keyword == "na" and "namaste" in normalized_input:
                    continue  # Skip if "na" is part of "namaste"
                logger.info(f"🚫 Fallback - Not interested: '{transcript}' (keyword: {keyword})")
                return "NOT_INTERESTED"
        
        # Check for interested keywords
        for keyword in interested_keywords:
            if keyword in normalized_input:
                logger.info(f"✅ Fallback - Interested: '{transcript}' (keyword: {keyword})")
                return "INTERESTED"
        
        # Special handling for common patterns
        # If customer mentions time (like "5 baje", "kal", etc.), they likely want to end call
        time_patterns = ["baje", "o'clock", "time", "kal", "parso", "baad mein"]
        if any(pattern in normalized_input for pattern in time_patterns):
            logger.info(f"🚫 Fallback - Time mentioned (likely not interested): '{transcript}'")
            return "NOT_INTERESTED"
        
        # If customer says hello/hi only, it's unclear
        greeting_only = ["hello", "hi", "hey", "namaste", "namaskar"]
        if any(greeting in normalized_input for greeting in greeting_only) and len(normalized_input.split()) <= 2:
            logger.info(f"❓ Fallback - Greeting only (unclear): '{transcript}'")
            return "UNCLEAR"
        
        # Default to unclear for anything else
        logger.info(f"❓ Fallback - Unclear: '{transcript}'")
        return "UNCLEAR" 