"""
Professional MongoDB logging service for voice bot conversations

Features:
- End-to-end conversation tracking
- Performance metrics logging
- Error tracking and debugging
- Session management
- Audio metadata logging
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional

from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

from config import MONGODB_CONFIG, CONVERSATION_LOGGING

logger = logging.getLogger(__name__)


class ConversationLogger:
    """
    Professional MongoDB logging service for voice bot conversations
    
    Features:
    - End-to-end conversation tracking
    - Performance metrics logging
    - Error tracking and debugging
    - Session management
    - Audio metadata logging
    """
    
    def __init__(self):
        """Initialize MongoDB connection and logging setup"""
        self.client = None
        self.db = None
        self.collection = None
        self._initialize_mongodb()
        
    def _initialize_mongodb(self):
        """Initialize MongoDB connection with proper error handling"""
        try:
            logger.info("🔌 Initializing MongoDB connection for conversation logging...")
            
            # Create MongoDB client with connection options
            self.client = MongoClient(
                MONGODB_CONFIG["connection_string"],
                serverSelectionTimeoutMS=MONGODB_CONFIG["server_selection_timeout"],
                connectTimeoutMS=MONGODB_CONFIG["connection_timeout"],
                maxPoolSize=MONGODB_CONFIG["max_pool_size"],
                minPoolSize=MONGODB_CONFIG["min_pool_size"],
                maxIdleTimeMS=MONGODB_CONFIG["max_idle_time"],
                retryWrites=MONGODB_CONFIG["retry_writes"],
                retryReads=MONGODB_CONFIG["retry_reads"]
            )
            
            # Test connection
            self.client.admin.command('ping')
            logger.info("✅ MongoDB connection established successfully")
            
            # Initialize database and collection
            self.db = self.client[MONGODB_CONFIG["database_name"]]
            self.collection = self.db[MONGODB_CONFIG["collection_name"]]
            
            # Create indexes for better query performance
            self._create_indexes()
            
            logger.info(f"📊 MongoDB logging initialized: {MONGODB_CONFIG['database_name']}.{MONGODB_CONFIG['collection_name']}")
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.error(f"❌ MongoDB connection failed: {e}")
            self.client = None
            self.db = None
            self.collection = None
        except Exception as e:
            logger.error(f"❌ MongoDB initialization error: {e}")
            self.client = None
            self.db = None
            self.collection = None
    
    def _create_indexes(self):
        """Create indexes for better query performance"""
        try:
            # Index on session_id for fast session queries
            self.collection.create_index("session_id")
            
            # Index on timestamp for time-based queries
            self.collection.create_index("timestamp")
            
            # Index on customer_intent for intent analysis
            self.collection.create_index("customer_intent")
            
            # Index on session_mode for mode-based queries
            self.collection.create_index("session_mode")
            
            # Compound index for session and timestamp
            self.collection.create_index([("session_id", 1), ("timestamp", -1)])
            
            logger.info("✅ MongoDB indexes created successfully")
            
        except Exception as e:
            logger.error(f"❌ Error creating MongoDB indexes: {e}")
    
    def log_session_start(self, session_id: str, session_mode: str = "normal_conversation", 
                         client_info: Dict[str, Any] = None) -> str:
        """Log session start event"""
        try:
            if self.collection is None:
                logger.warning("⚠️ MongoDB not available for logging")
                return None
                
            log_entry = {
                "_id": str(uuid.uuid4()),
                "session_id": session_id,
                "event_type": "session_start",
                "session_mode": session_mode,
                "timestamp": datetime.utcnow(),
                "client_info": client_info or {},
                "status": "active",
                "conversation_flow": [],
                "performance_metrics": {
                    "total_audio_chunks": 0,
                    "total_processing_time": 0.0,
                    "average_response_time": 0.0,
                    "errors_count": 0
                },
                "metadata": {
                    "bot_version": "1.0.0",
                    "phase": "phase1_simple_intent",
                    "logging_enabled": CONVERSATION_LOGGING["enabled"]
                }
            }
            
            result = self.collection.insert_one(log_entry)
            logger.info(f"📝 Logged session start: {session_id} -> {result.inserted_id}")
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"❌ Error logging session start: {e}")
            return None
    
    def log_audio_received(self, session_id: str, audio_metadata: Dict[str, Any], 
                          processing_time: float = None) -> str:
        """Log audio received event"""
        try:
            if self.collection is None:
                return None
                
            log_entry = {
                "_id": str(uuid.uuid4()),
                "session_id": session_id,
                "event_type": "audio_received",
                "timestamp": datetime.utcnow(),
                "audio_metadata": {
                    "size_bytes": audio_metadata.get("size_bytes", 0),
                    "duration_ms": audio_metadata.get("duration_ms", 0),
                    "format": audio_metadata.get("format", "unknown"),
                    "sample_rate": audio_metadata.get("sample_rate", 0),
                    "channels": audio_metadata.get("channels", 1)
                },
                "processing_time_ms": processing_time * 1000 if processing_time else None,
                "source": audio_metadata.get("source", "unknown")
            }
            
            result = self.collection.insert_one(log_entry)
            logger.debug(f"📝 Logged audio received: {session_id} ({audio_metadata.get('size_bytes', 0)} bytes)")
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"❌ Error logging audio received: {e}")
            return None
    
    def log_speech_recognition(self, session_id: str, transcript: str, confidence: float,
                              processing_time: float = None, language: str = "en-IN") -> str:
        """Log speech recognition results"""
        try:
            if self.collection is None:
                return None
                
            log_entry = {
                "_id": str(uuid.uuid4()),
                "session_id": session_id,
                "event_type": "speech_recognition",
                "timestamp": datetime.utcnow(),
                "transcript": transcript,
                "confidence": confidence,
                "language": language,
                "processing_time_ms": processing_time * 1000 if processing_time else None,
                "word_count": len(transcript.split()),
                "character_count": len(transcript)
            }
            
            result = self.collection.insert_one(log_entry)
            logger.debug(f"📝 Logged speech recognition: '{transcript}' (confidence: {confidence:.2f})")
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"❌ Error logging speech recognition: {e}")
            return None
    
    def log_customer_intent(self, session_id: str, transcript: str, detected_intent: str,
                           ai_analysis: str = None, processing_time: float = None) -> str:
        """Log customer intent detection"""
        try:
            if self.collection is None:
                return None
                
            log_entry = {
                "_id": str(uuid.uuid4()),
                "session_id": session_id,
                "event_type": "customer_intent",
                "timestamp": datetime.utcnow(),
                "customer_input": transcript,
                "detected_intent": detected_intent,
                "ai_analysis": ai_analysis,
                "processing_time_ms": processing_time * 1000 if processing_time else None,
                "intent_confidence": "high" if detected_intent in ["INTERESTED", "NOT_INTERESTED"] else "medium"
            }
            
            result = self.collection.insert_one(log_entry)
            logger.info(f"📝 Logged customer intent: {detected_intent} for '{transcript}'")
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"❌ Error logging customer intent: {e}")
            return None
    
    def log_ai_response(self, session_id: str, ai_response: str, response_type: str,
                       processing_time: float = None, audio_size: int = None) -> str:
        """Log AI response generation"""
        try:
            if self.collection is None:
                return None
                
            log_entry = {
                "_id": str(uuid.uuid4()),
                "session_id": session_id,
                "event_type": "ai_response",
                "timestamp": datetime.utcnow(),
                "ai_response": ai_response,
                "response_type": response_type,  # "greeting", "interested", "not_interested", "unclear"
                "processing_time_ms": processing_time * 1000 if processing_time else None,
                "audio_size_bytes": audio_size,
                "word_count": len(ai_response.split()),
                "character_count": len(ai_response)
            }
            
            result = self.collection.insert_one(log_entry)
            logger.debug(f"📝 Logged AI response: {response_type} ({len(ai_response)} chars)")
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"❌ Error logging AI response: {e}")
            return None
    
    def log_session_end(self, session_id: str, reason: str = "completed", 
                       total_duration: float = None, conversation_summary: Dict[str, Any] = None) -> str:
        """Log session end event"""
        try:
            if self.collection is None:
                return None
                
            log_entry = {
                "_id": str(uuid.uuid4()),
                "session_id": session_id,
                "event_type": "session_end",
                "timestamp": datetime.utcnow(),
                "end_reason": reason,
                "total_duration_seconds": total_duration,
                "conversation_summary": conversation_summary or {},
                "status": "completed"
            }
            
            result = self.collection.insert_one(log_entry)
            logger.info(f"📝 Logged session end: {session_id} - {reason}")
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"❌ Error logging session end: {e}")
            return None
    
    def log_error(self, session_id: str, error_type: str, error_message: str,
                  stack_trace: str = None, context: Dict[str, Any] = None) -> str:
        """Log error events"""
        try:
            if self.collection is None:
                return None
                
            log_entry = {
                "_id": str(uuid.uuid4()),
                "session_id": session_id,
                "event_type": "error",
                "timestamp": datetime.utcnow(),
                "error_type": error_type,
                "error_message": error_message,
                "stack_trace": stack_trace,
                "context": context or {},
                "severity": "high" if "fatal" in error_type.lower() else "medium"
            }
            
            result = self.collection.insert_one(log_entry)
            logger.error(f"📝 Logged error: {error_type} - {error_message}")
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"❌ Error logging error event: {e}")
            return None
    
    def log_performance_metric(self, session_id: str, metric_name: str, value: float,
                              unit: str = "ms", context: Dict[str, Any] = None) -> str:
        """Log performance metrics"""
        try:
            if self.collection is None:
                return None
                
            log_entry = {
                "_id": str(uuid.uuid4()),
                "session_id": session_id,
                "event_type": "performance_metric",
                "timestamp": datetime.utcnow(),
                "metric_name": metric_name,
                "value": value,
                "unit": unit,
                "context": context or {}
            }
            
            result = self.collection.insert_one(log_entry)
            logger.debug(f"📝 Logged performance metric: {metric_name} = {value} {unit}")
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"❌ Error logging performance metric: {e}")
            return None
    
    def get_session_logs(self, session_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Retrieve logs for a specific session"""
        try:
            if self.collection is None:
                return []
                
            logs = list(self.collection.find(
                {"session_id": session_id},
                {"_id": 0}  # Exclude MongoDB _id
            ).sort("timestamp", 1).limit(limit))
            
            logger.info(f"📊 Retrieved {len(logs)} logs for session {session_id}")
            return logs
            
        except Exception as e:
            logger.error(f"❌ Error retrieving session logs: {e}")
            return []
    
    def close_connection(self):
        """Close MongoDB connection"""
        try:
            if self.client:
                self.client.close()
                logger.info("🔌 MongoDB connection closed")
        except Exception as e:
            logger.error(f"❌ Error closing MongoDB connection: {e}")
    
    def get_conversation_summary(self, session_id: str) -> Dict[str, Any]:
        """Get conversation summary for debugging"""
        try:
            logs = self.get_session_logs(session_id)
            
            summary = {
                "session_id": session_id,
                "total_events": len(logs),
                "events_by_type": {},
                "performance_metrics": {},
                "errors": [],
                "conversation_flow": []
            }
            
            for log in logs:
                event_type = log.get("event_type", "unknown")
                summary["events_by_type"][event_type] = summary["events_by_type"].get(event_type, 0) + 1
                
                if event_type == "performance_metric":
                    metric_name = log.get("metric_name", "unknown")
                    summary["performance_metrics"][metric_name] = log.get("value", 0)
                elif event_type == "error":
                    summary["errors"].append({
                        "type": log.get("error_type", "unknown"),
                        "message": log.get("error_message", "unknown"),
                        "timestamp": log.get("timestamp")
                    })
                elif event_type in ["speech_recognition", "customer_intent", "ai_response"]:
                    summary["conversation_flow"].append({
                        "type": event_type,
                        "content": log.get("transcript") or log.get("customer_input") or log.get("ai_response", ""),
                        "timestamp": log.get("timestamp")
                    })
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ Error getting conversation summary: {e}")
            return {"error": str(e)} 