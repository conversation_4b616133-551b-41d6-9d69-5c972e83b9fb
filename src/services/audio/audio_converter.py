"""
Audio converter service for handling different audio formats
"""

import logging
import io
from typing import Optional

from pydub import AudioSegment

logger = logging.getLogger(__name__)


class AudioConverter:
    """
    Professional audio converter service for voice bot
    
    Features:
    - Multiple audio format support
    - Sample rate conversion
    - Channel conversion
    - Format optimization for speech recognition
    """
    
    def __init__(self):
        """Initialize audio converter service"""
        logger.info("🎵 Initializing Audio Converter service...")
        logger.info("✅ Audio converter initialized")
    
    async def convert_for_speech_recognition(self, audio_data: bytes) -> Optional[bytes]:
        """
        Convert audio to format suitable for Google Speech Recognition
        
        Args:
            audio_data: Raw audio data in bytes
            
        Returns:
            Converted audio data in WAV format
        """
        try:
            logger.info(f"🎵 Converting audio for speech recognition: {len(audio_data)} bytes")
            
            # Create AudioSegment from raw audio data
            audio_segment = AudioSegment(
                data=audio_data,
                sample_width=2,  # 16-bit = 2 bytes
                frame_rate=16000,  # 16kHz
                channels=1  # Mono
            )
            
            # Export as WAV format for Google Speech Recognition
            wav_buffer = io.BytesIO()
            audio_segment.export(wav_buffer, format="wav")
            wav_data = wav_buffer.getvalue()
            
            logger.info(f"✅ Converted audio for speech recognition: {len(audio_data)} -> {len(wav_data)} bytes")
            return wav_data
            
        except Exception as e:
            logger.error(f"❌ Error converting audio for speech recognition: {e}")
            return audio_data
    
    async def convert_dialer_audio_format(self, audio_data: bytes) -> Optional[bytes]:
        """
        Convert audio from dialer format (8kHz) to processing format (16kHz)
        
        Args:
            audio_data: Raw audio data from dialer
            
        Returns:
            Converted audio data
        """
        try:
            logger.info(f"🎵 Converting dialer audio format: {len(audio_data)} bytes")
            
            # Create AudioSegment from raw audio data
            # Dialer sends 8kHz, 16-bit PCM, mono
            audio_segment = AudioSegment(
                data=audio_data,
                sample_width=2,  # 16-bit = 2 bytes
                frame_rate=8000,  # 8kHz
                channels=1  # Mono
            )
            
            # Convert to 16kHz for Google Speech Recognition
            audio_segment = audio_segment.set_frame_rate(16000)
            
            # Export as WAV format
            wav_buffer = io.BytesIO()
            audio_segment.export(wav_buffer, format="wav")
            wav_data = wav_buffer.getvalue()
            
            logger.info(f"✅ Converted dialer audio: {len(audio_data)} -> {len(wav_data)} bytes")
            return wav_data
            
        except Exception as e:
            logger.error(f"❌ Error converting dialer audio format: {e}")
            return audio_data
    
    async def convert_ui_audio_format(self, audio_data: bytes, source_format: str = "webm") -> Optional[bytes]:
        """
        Convert UI audio format (WebM) to processing format
        
        Args:
            audio_data: Raw audio data from UI
            source_format: Source audio format (default: webm)
            
        Returns:
            Converted audio data
        """
        try:
            logger.info(f"🎵 Converting UI audio format: {len(audio_data)} bytes ({source_format})")
            
            # Create AudioSegment from WebM audio data
            audio_segment = AudioSegment.from_file(io.BytesIO(audio_data), format=source_format)
            
            # Convert to 16kHz mono for Google Speech Recognition
            audio_segment = audio_segment.set_frame_rate(16000)
            audio_segment = audio_segment.set_channels(1)
            audio_segment = audio_segment.set_sample_width(2)  # Force 16-bit
            
            # Export as WAV format with 16-bit samples
            wav_buffer = io.BytesIO()
            audio_segment.export(wav_buffer, format="wav", parameters=["-ac", "1", "-ar", "16000"])
            wav_data = wav_buffer.getvalue()
            
            logger.info(f"✅ Converted UI audio: {len(audio_data)} -> {len(wav_data)} bytes")
            return wav_data
            
        except Exception as e:
            logger.error(f"❌ Error converting UI audio format: {e}")
            # Try alternative conversion method
            try:
                logger.info("🔄 Trying alternative audio conversion method...")
                
                # Use ffmpeg parameters for better WebM handling
                wav_buffer = io.BytesIO()
                audio_segment.export(
                    wav_buffer, 
                    format="wav",
                    parameters=[
                        "-ac", "1",           # Mono
                        "-ar", "16000",       # 16kHz sample rate
                        "-acodec", "pcm_s16le" # 16-bit PCM
                    ]
                )
                wav_data = wav_buffer.getvalue()
                
                logger.info(f"✅ Alternative conversion successful: {len(audio_data)} -> {len(wav_data)} bytes")
                return wav_data
                
            except Exception as e2:
                logger.error(f"❌ Alternative conversion also failed: {e2}")
                # For UI testing, return original audio if conversion fails
                # In production, dialer will send proper 16-bit PCM audio
                return audio_data
    
    async def convert_audio_format(self, audio_data: bytes, source_format: str = "unknown", 
                                 target_format: str = "wav") -> Optional[bytes]:
        """
        Generic audio format converter
        
        Args:
            audio_data: Raw audio data
            source_format: Source audio format
            target_format: Target audio format
            
        Returns:
            Converted audio data
        """
        try:
            logger.info(f"🎵 Converting audio format: {source_format} -> {target_format} ({len(audio_data)} bytes)")
            
            # Create AudioSegment from audio data
            audio_segment = AudioSegment.from_file(io.BytesIO(audio_data), format=source_format)
            
            # Convert to target format
            output_buffer = io.BytesIO()
            audio_segment.export(output_buffer, format=target_format)
            converted_data = output_buffer.getvalue()
            
            logger.info(f"✅ Audio format conversion successful: {len(audio_data)} -> {len(converted_data)} bytes")
            return converted_data
            
        except Exception as e:
            logger.error(f"❌ Error converting audio format: {e}")
            return audio_data 