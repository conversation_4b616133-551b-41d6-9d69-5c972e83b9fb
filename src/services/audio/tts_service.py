"""
Text-to-Speech service using Google Cloud TTS
"""

import logging
import time
from typing import Optional

from google.cloud import texttospeech

from config import TTS_CONFIG

logger = logging.getLogger(__name__)


class TTSService:
    """
    Professional Text-to-Speech service using Google Cloud TTS
    
    Features:
    - High-quality voice synthesis
    - Multiple voice options
    - Performance monitoring
    - Audio format optimization
    """
    
    def __init__(self):
        """Initialize TTS service"""
        try:
            logger.info("🔊 Initializing Text-to-Speech service...")
            
            # Initialize Text-to-Speech client
            self.tts_client = texttospeech.TextToSpeechClient()
            self.tts_config = texttospeech.VoiceSelectionParams(
                language_code=TTS_CONFIG["language_code"],
                name=TTS_CONFIG["voice_name"],
                ssml_gender=TTS_CONFIG["ssml_gender"]
            )
            self.audio_config = texttospeech.AudioConfig(
                audio_encoding=TTS_CONFIG["audio_encoding"],
                sample_rate_hertz=TTS_CONFIG["sample_rate_hertz"]
            )
            logger.info("✅ TTS config created")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize TTS service: {e}")
            raise
    
    async def synthesize_speech(self, text: str) -> Optional[bytes]:
        """
        Convert text to speech
        
        Args:
            text: Text to convert to speech
            
        Returns:
            Audio data in bytes or None if failed
        """
        try:
            start_time = time.time()
            logger.info(f"🔊 Converting to speech: '{text}'")
            
            synthesis_input = texttospeech.SynthesisInput(text=text)
            
            response = self.tts_client.synthesize_speech(
                input=synthesis_input,
                voice=self.tts_config,
                audio_config=self.audio_config
            )
            
            tts_time = time.time() - start_time
            logger.info(f"✅ TTS completed in {tts_time:.2f}s (size: {len(response.audio_content)} bytes)")
            
            return response.audio_content
            
        except Exception as e:
            logger.error(f"❌ Error in text-to-speech: {e}")
            return None
    
    async def synthesize_speech_with_metadata(self, text: str, session_id: str = None) -> Optional[dict]:
        """
        Convert text to speech with additional metadata
        
        Args:
            text: Text to convert to speech
            session_id: Session ID for logging
            
        Returns:
            Dictionary with audio data and metadata
        """
        try:
            start_time = time.time()
            
            audio_content = await self.synthesize_speech(text)
            
            if audio_content is None:
                return None
            
            tts_time = time.time() - start_time
            
            return {
                "audio_content": audio_content,
                "text": text,
                "processing_time": tts_time,
                "audio_size": len(audio_content),
                "word_count": len(text.split()),
                "character_count": len(text)
            }
            
        except Exception as e:
            logger.error(f"❌ Error in TTS with metadata: {e}")
            return None 