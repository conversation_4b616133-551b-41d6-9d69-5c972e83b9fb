"""
Speech recognition service using Google Cloud Speech-to-Text
"""

import logging
import time
from typing import Op<PERSON>, Tuple

from google.cloud import speech_v1

from config import SPEECH_CONFIG

logger = logging.getLogger(__name__)


class SpeechService:
    """
    Professional speech recognition service using Google Cloud Speech-to-Text
    
    Features:
    - High-quality speech recognition
    - Multi-language support
    - Confidence scoring
    - Performance monitoring
    """
    
    def __init__(self):
        """Initialize speech recognition service"""
        try:
            logger.info("🎤 Initializing Speech Recognition service...")
            
            # Initialize Speech-to-Text client
            self.speech_client = speech_v1.SpeechClient()
            self.speech_config = speech_v1.RecognitionConfig(
                language_code=SPEECH_CONFIG["language_code"],
                enable_automatic_punctuation=SPEECH_CONFIG["enable_automatic_punctuation"],
                enable_word_time_offsets=SPEECH_CONFIG["enable_word_time_offsets"],
                model=SPEECH_CONFIG["model"],
                use_enhanced=SPEECH_CONFIG["use_enhanced"]
            )
            logger.info("✅ Speech recognition config created")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize speech recognition service: {e}")
            raise
    
    async def recognize_speech(self, audio_data: bytes) -> Optional[Tuple[str, float]]:
        """
        Recognize speech from audio data
        
        Args:
            audio_data: Raw audio data in bytes
            
        Returns:
            Tuple of (transcript, confidence) or None if no speech detected
        """
        try:
            start_time = time.time()
            logger.info(f"🎤 Recognizing speech from {len(audio_data)} bytes of audio")
            
            # Create recognition audio object
            audio = speech_v1.RecognitionAudio(content=audio_data)
            
            # Perform speech recognition
            response = self.speech_client.recognize(
                config=self.speech_config,
                audio=audio
            )
            
            processing_time = time.time() - start_time
            logger.info(f"🕒 Speech recognition completed in {processing_time:.2f}s")
            
            if not response.results:
                logger.warning("⚠️ No speech detected in audio")
                return None
            
            # Get transcribed text and confidence
            transcript = response.results[0].alternatives[0].transcript
            confidence = response.results[0].alternatives[0].confidence
            
            logger.info(f"📝 Speech recognition result: '{transcript}' (confidence: {confidence:.2f})")
            
            return transcript, confidence
            
        except Exception as e:
            logger.error(f"❌ Error in speech recognition: {e}")
            return None
    
    async def recognize_speech_with_metadata(self, audio_data: bytes, session_id: str = None) -> Optional[dict]:
        """
        Recognize speech with additional metadata
        
        Args:
            audio_data: Raw audio data in bytes
            session_id: Session ID for logging
            
        Returns:
            Dictionary with transcript, confidence, and processing time
        """
        try:
            start_time = time.time()
            
            result = await self.recognize_speech(audio_data)
            
            if result is None:
                return None
            
            transcript, confidence = result
            processing_time = time.time() - start_time
            
            return {
                "transcript": transcript,
                "confidence": confidence,
                "processing_time": processing_time,
                "audio_size": len(audio_data)
            }
            
        except Exception as e:
            logger.error(f"❌ Error in speech recognition with metadata: {e}")
            return None 