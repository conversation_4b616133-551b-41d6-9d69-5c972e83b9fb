"""
Session utility functions for voice bot sessions
"""

import time
import logging
from typing import Dict, Any, List, Optional

from src.models.session_state import SessionState

logger = logging.getLogger(__name__)


class SessionUtils:
    """
    Utility functions for managing voice bot sessions
    
    Features:
    - Session state management
    - Session cleanup
    - Session validation
    - Session statistics
    """
    
    def __init__(self):
        """Initialize session utilities"""
        self._session_states: Dict[str, SessionState] = {}
        self._session_modes: Dict[str, str] = {}
        self._greeting_sent: Dict[str, bool] = {}
        self._session_timestamps: Dict[str, float] = {}
        self._last_cleanup_time = time.time()
        logger.info("🔄 Session utilities initialized")
    
    def initialize_session_state(self, session_id: str) -> SessionState:
        """
        Initialize session state for a new session
        
        Args:
            session_id: Session identifier
            
        Returns:
            SessionState object
        """
        if session_id not in self._session_states:
            session_state = SessionState(session_id=session_id)
            self._session_states[session_id] = session_state
            self._session_timestamps[session_id] = time.time()
            logger.info(f"🔄 Initialized session state for {session_id}")
        
        return self._session_states[session_id]
    
    def update_session_activity(self, session_id: str):
        """
        Update session activity timestamp
        
        Args:
            session_id: Session identifier
        """
        if session_id in self._session_states:
            self._session_states[session_id].update_activity()
            self._session_timestamps[session_id] = time.time()
    
    def cleanup_expired_sessions(self, timeout_seconds: int = 300) -> List[str]:
        """
        Clean up expired sessions
        
        Args:
            timeout_seconds: Session timeout in seconds
            
        Returns:
            List of cleaned up session IDs
        """
        current_time = time.time()
        expired_sessions = []
        
        for session_id, timestamp in self._session_timestamps.items():
            if current_time - timestamp > timeout_seconds:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            logger.info(f"🧹 Cleaning up expired session: {session_id}")
            self._remove_session(session_id)
        
        return expired_sessions
    
    def _remove_session(self, session_id: str):
        """
        Remove session and all associated data
        
        Args:
            session_id: Session identifier
        """
        if session_id in self._session_states:
            del self._session_states[session_id]
        if session_id in self._session_modes:
            del self._session_modes[session_id]
        if session_id in self._greeting_sent:
            del self._greeting_sent[session_id]
        if session_id in self._session_timestamps:
            del self._session_timestamps[session_id]
    
    def get_session_state(self, session_id: str) -> Optional[SessionState]:
        """
        Get session state
        
        Args:
            session_id: Session identifier
            
        Returns:
            SessionState object or None if not found
        """
        return self._session_states.get(session_id)
    
    def set_session_mode(self, session_id: str, mode: str):
        """
        Set session mode
        
        Args:
            session_id: Session identifier
            mode: Session mode
        """
        self._session_modes[session_id] = mode
        if session_id in self._session_states:
            self._session_states[session_id].session_mode = mode
        logger.info(f"🎯 Set session {session_id} mode to {mode}")
    
    def get_session_mode(self, session_id: str) -> str:
        """
        Get session mode
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session mode string
        """
        return self._session_modes.get(session_id, "normal_conversation")
    
    def set_greeting_sent(self, session_id: str, sent: bool = True):
        """
        Set greeting sent flag
        
        Args:
            session_id: Session identifier
            sent: Whether greeting was sent
        """
        self._greeting_sent[session_id] = sent
        if session_id in self._session_states:
            self._session_states[session_id].greeting_sent = sent
    
    def is_greeting_sent(self, session_id: str) -> bool:
        """
        Check if greeting was sent for session
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if greeting was sent, False otherwise
        """
        return self._greeting_sent.get(session_id, False)
    
    def get_session_stats(self) -> Dict[str, Any]:
        """
        Get session statistics
        
        Returns:
            Dictionary with session statistics
        """
        total_sessions = len(self._session_states)
        active_sessions = len([s for s in self._session_states.values() if not s.is_expired()])
        
        mode_stats = {}
        for mode in self._session_modes.values():
            mode_stats[mode] = mode_stats.get(mode, 0) + 1
        
        return {
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "expired_sessions": total_sessions - active_sessions,
            "mode_distribution": mode_stats,
            "greeting_sent_count": sum(self._greeting_sent.values()),
            "last_cleanup_time": self._last_cleanup_time
        }
    
    def get_all_sessions(self) -> List[SessionState]:
        """
        Get all session states
        
        Returns:
            List of all SessionState objects
        """
        return list(self._session_states.values())
    
    def validate_session(self, session_id: str) -> bool:
        """
        Validate if session exists and is active
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if session is valid and active, False otherwise
        """
        if session_id not in self._session_states:
            return False
        
        session_state = self._session_states[session_id]
        return not session_state.is_expired()
    
    def force_cleanup_all_sessions(self):
        """Force cleanup of all sessions"""
        logger.info("🧹 Force cleaning up all sessions")
        session_ids = list(self._session_states.keys())
        for session_id in session_ids:
            self._remove_session(session_id)
        logger.info(f"✅ Cleaned up {len(session_ids)} sessions")
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed session information
        
        Args:
            session_id: Session identifier
            
        Returns:
            Dictionary with session information or None if not found
        """
        if session_id not in self._session_states:
            return None
        
        session_state = self._session_states[session_id]
        return {
            "session_id": session_id,
            "state": session_state.to_dict(),
            "mode": self._session_modes.get(session_id, "unknown"),
            "greeting_sent": self._greeting_sent.get(session_id, False),
            "last_activity": self._session_timestamps.get(session_id, 0),
            "is_expired": session_state.is_expired()
        } 