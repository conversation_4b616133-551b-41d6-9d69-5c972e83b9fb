"""
Message utility functions for WebSocket communication
"""

import json
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class MessageUtils:
    """
    Utility functions for handling WebSocket messages
    
    Features:
    - Message parsing and validation
    - Message serialization
    - Error handling
    - Message type detection
    """
    
    @staticmethod
    def parse_message(message: str, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Parse WebSocket message
        
        Args:
            message: Raw message string
            session_id: Session ID for logging
            
        Returns:
            Parsed message dictionary or None if invalid
        """
        try:
            data = json.loads(message)
            logger.info(f"✅ JSON parsed successfully for {session_id}")
            return data
        except json.JSONDecodeError as e:
            logger.error(f"❌ Invalid JSON from {session_id}: {e}")
            return None
    
    @staticmethod
    def serialize_message(data: Dict[str, Any]) -> str:
        """
        Serialize message to JSON string
        
        Args:
            data: Message data dictionary
            
        Returns:
            JSON string
        """
        try:
            return json.dumps(data)
        except Exception as e:
            logger.error(f"❌ Error serializing message: {e}")
            return json.dumps({"error": "serialization_failed"})
    
    @staticmethod
    def get_message_type(data: Dict[str, Any]) -> str:
        """
        Get message type from data
        
        Args:
            data: Message data dictionary
            
        Returns:
            Message type string
        """
        # Check for explicit type
        if "type" in data:
            return data["type"]
        
        # Check for event-based messages
        if "event" in data:
            return f"event_{data['event']}"
        
        # Check for signal messages
        if "signal" in data:
            return "signal"
        
        return "unknown"
    
    @staticmethod
    def validate_audio_message(data: Dict[str, Any]) -> bool:
        """
        Validate audio message format
        
        Args:
            data: Message data dictionary
            
        Returns:
            True if valid, False otherwise
        """
        required_fields = ["type", "data"]
        
        # Check required fields
        for field in required_fields:
            if field not in data:
                logger.warning(f"⚠️ Missing required field '{field}' in audio message")
                return False
        
        # Check message type
        if data["type"] != "audio":
            logger.warning(f"⚠️ Invalid message type for audio message: {data['type']}")
            return False
        
        # Check data field
        if not data["data"]:
            logger.warning("⚠️ Empty audio data")
            return False
        
        return True
    
    @staticmethod
    def validate_text_message(data: Dict[str, Any]) -> bool:
        """
        Validate text message format
        
        Args:
            data: Message data dictionary
            
        Returns:
            True if valid, False otherwise
        """
        required_fields = ["type", "data"]
        
        # Check required fields
        for field in required_fields:
            if field not in data:
                logger.warning(f"⚠️ Missing required field '{field}' in text message")
                return False
        
        # Check message type
        if data["type"] != "text":
            logger.warning(f"⚠️ Invalid message type for text message: {data['type']}")
            return False
        
        # Check data field
        if not data["data"]:
            logger.warning("⚠️ Empty text data")
            return False
        
        return True
    
    @staticmethod
    def create_error_message(error_type: str, error_message: str) -> Dict[str, Any]:
        """
        Create error message
        
        Args:
            error_type: Type of error
            error_message: Error message
            
        Returns:
            Error message dictionary
        """
        return {
            "type": "error",
            "error_type": error_type,
            "error_message": error_message,
            "timestamp": "now"
        }
    
    @staticmethod
    def create_audio_response(audio_b64: str, text: str, customer_intent: str = "", 
                            next_action: str = "continue") -> Dict[str, Any]:
        """
        Create audio response message
        
        Args:
            audio_b64: Base64 encoded audio data
            text: Response text
            customer_intent: Detected customer intent
            next_action: Next action to take
            
        Returns:
            Audio response message dictionary
        """
        return {
            "type": "audio",
            "data": audio_b64,
            "text": text,
            "customer_intent": customer_intent,
            "next_action": next_action
        }
    
    @staticmethod
    def create_signal_message(signal_data: str, reason: str = "") -> Dict[str, Any]:
        """
        Create signal message
        
        Args:
            signal_data: Signal data
            reason: Reason for signal
            
        Returns:
            Signal message dictionary
        """
        return {
            "type": "signal",
            "data": signal_data,
            "reason": reason
        } 