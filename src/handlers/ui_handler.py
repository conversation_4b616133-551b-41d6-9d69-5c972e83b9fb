"""
UI handler for processing UI-specific messages
"""

import base64
import logging
import time
from typing import Dict, Any, Optional

from websockets.server import WebSocketServerProtocol

logger = logging.getLogger(__name__)


class UIHandler:
    """
    Handler for UI testing interface messages
    
    Features:
    - Audio processing for UI format
    - Full AI conversation (Phase 2)
    - Text message handling
    - Session management
    """
    
    def __init__(self, voice_bot):
        """Initialize UI handler"""
        self.voice_bot = voice_bot
        logger.info("🖥️ UI handler initialized")
    
    async def handle_audio_message(self, websocket: WebSocketServerProtocol, session_id: str, data: dict):
        """Handle audio messages from UI"""
        try:
            # Initialize session state if needed
            self.voice_bot._initialize_session_state(session_id)
            self.voice_bot._update_session_activity(session_id)
            
            audio_b64 = data.get('data', '')
            if not audio_b64:
                logger.warning(f"⚠️ No audio data in UI message from {session_id}")
                return
            
            # Get mode (dialer or conversation)
            mode = data.get('mode', 'conversation')
            logger.info(f"🎤 Processing audio in {mode} mode for {session_id}")
            
            audio_chunk = base64.b64decode(audio_b64)
            logger.info(f"🎤 Received UI audio chunk: {len(audio_chunk)} bytes from {session_id}")
            
            # Log audio received
            audio_metadata = {
                "size_bytes": len(audio_chunk),
                "format": "base64_encoded",
                "source": "ui",
                "sample_rate": 16000,  # UI format
                "channels": 1,
                "mode": mode
            }
            self.voice_bot.conversation_logger.log_audio_received(session_id, audio_metadata)
            
            # VDR: Check if AI is currently speaking and detect interruption
            session_state = self.voice_bot._session_states[session_id]
            if session_state.is_speaking:
                logger.info(f"⚠️ Customer interruption detected in session {session_id}")
                session_state.interrupted = True
                session_state.is_speaking = False
                # Don't process this audio chunk, wait for TTS to stop
                return
            
            # Convert audio format
            converted_audio = await self.voice_bot.audio_converter.convert_ui_audio_format(audio_chunk, "webm")
            
            # Process audio based on mode
            if mode == 'dialer':
                response = await self._process_ui_audio_dialer(converted_audio, session_id)
            else:  # conversation mode (default)
                response = await self._process_ui_audio_full_ai(converted_audio, session_id)
            
            logger.info(f"✅ Processed UI audio in {mode} mode, response: {response is not None}")
            
            if response:
                # VDR: Set speaking state before sending audio
                session_state = self.voice_bot._session_states[session_id]
                session_state.is_speaking = True
                session_state.interrupted = False
                
                # Send response back to UI
                response_message = {
                    'type': 'audio',
                    'data': response['audio_b64'],
                    'text': response.get('ai_response', ''),
                    'customer_intent': response.get('transcribed_text', ''),
                    'next_action': response.get('action', 'continue'),
                    'user_transcript': response.get('transcribed_text', '')  # Add user's transcribed speech
                }
                await websocket.send(self.voice_bot._serialize_message(response_message))
                logger.info(f"📤 Sent UI audio response to {session_id}")
                
                # VDR: Clear speaking state after sending
                session_state = self.voice_bot._session_states[session_id]
                session_state.is_speaking = False
                
                # Check if session should end
                if response.get('action') == 'end_call':
                    return
                    
        except Exception as e:
            logger.error(f"❌ Error handling UI audio message: {e}")
    
    async def _process_ui_audio_full_ai(self, audio_data: bytes, session_id: str) -> Optional[dict]:
        """Process UI audio with full AI conversation (Phase 2)"""
        try:
            logger.info(f"🎤 Processing UI audio with full AI for {session_id} ({len(audio_data)} bytes)")
            
            # Perform speech recognition
            stt_result = await self.voice_bot.speech_service.recognize_speech_with_metadata(audio_data, session_id)
            if not stt_result:
                logger.warning(f"⚠️ No speech detected in UI audio for {session_id}")
                return None
            
            transcript = stt_result["transcript"]
            confidence = stt_result["confidence"]
            stt_time = stt_result["processing_time"]
            logger.info(f"📝 UI transcription: '{transcript}' (confidence: {confidence:.2f})")
            
            # Log speech recognition
            self.voice_bot.conversation_logger.log_speech_recognition(session_id, transcript, confidence, stt_time)
            
            # Phase 2: Generate full AI response using Vertex AI
            ai_response = await self.voice_bot.gemini_service.generate_response(
                transcript, 
                self.voice_bot.conversation_context.get(session_id, [])
            )
            
            if ai_response:
                # Convert to speech
                response_audio = await self.voice_bot.tts_service.synthesize_speech(ai_response)
                
                if response_audio:
                    # Log AI response
                    self.voice_bot.conversation_logger.log_ai_response(
                        session_id, 
                        ai_response, 
                        "ai_conversation", 
                        None, 
                        len(response_audio)
                    )
                    
                    # Update conversation history
                    history = self.voice_bot.conversation_context.get(session_id, [])
                    history.append({"role": "user", "content": transcript})
                    history.append({"role": "assistant", "content": ai_response})
                    self.voice_bot.conversation_context[session_id] = history[-10:]  # Keep last 10 messages
                    
                    return {
                        'audio_b64': base64.b64encode(response_audio).decode('utf-8'),
                        'transcribed_text': transcript,
                        'ai_response': ai_response,
                        'action': 'continue',  # Always continue for full conversations
                        'reason': 'ai_conversation'
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error processing UI audio with full AI: {e}")
            return None

    async def _process_ui_audio_dialer(self, audio_data: bytes, session_id: str) -> Optional[dict]:
        """Process UI audio for dialer integration with predefined responses"""
        try:
            logger.info(f"🎤 Processing UI audio for dialer mode for {session_id} ({len(audio_data)} bytes)")
            
            # Perform speech recognition
            stt_result = await self.voice_bot.speech_service.recognize_speech_with_metadata(audio_data, session_id)
            if not stt_result:
                logger.warning(f"⚠️ No speech detected in UI audio for {session_id}")
                return None
            
            transcript = stt_result["transcript"]
            confidence = stt_result["confidence"]
            stt_time = stt_result["processing_time"]
            logger.info(f"📝 Dialer transcription: '{transcript}' (confidence: {confidence:.2f})")
            
            # Log speech recognition
            self.voice_bot.conversation_logger.log_speech_recognition(session_id, transcript, confidence, stt_time)
            
            # Use intent_analyzer to get predefined response
            intent_response = await self.voice_bot.intent_analyzer.analyze_customer_response(transcript, session_id)
            logger.info(f"🎯 Dialer intent analysis result: {intent_response}")
            
            if intent_response:
                ai_response = intent_response.get('message', '')
                action = intent_response.get('action', 'continue')
                reason = intent_response.get('reason', '')
                
                # Convert to speech
                response_audio = await self.voice_bot.tts_service.synthesize_speech(ai_response)
                
                if response_audio:
                    # Convert audio to base64
                    audio_b64 = base64.b64encode(response_audio).decode('utf-8')
                    
                    return {
                        'audio_b64': audio_b64,
                        'ai_response': ai_response,
                        'transcribed_text': transcript,
                        'intent': reason,
                        'action': action
                    }
                else:
                    logger.error(f"❌ Failed to synthesize speech for dialer response {session_id}")
                    return None
            else:
                logger.warning(f"⚠️ No dialer response generated for {session_id}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error processing UI audio for dialer: {e}")
            return None
    
    async def handle_text_message(self, websocket: WebSocketServerProtocol, session_id: str, data: dict):
        """Handle text messages from UI - Full AI conversation mode"""
        try:
            text_input = data.get('data', '')
            if not text_input:
                return
            
            logger.info(f"📝 Received text input from UI: '{text_input}'")
            
            # Send greeting if not sent yet
            if session_id not in self.voice_bot._greeting_sent or not self.voice_bot._greeting_sent[session_id]:
                self.voice_bot._greeting_sent[session_id] = True
                await self._send_greeting(websocket, session_id)
            
            # Phase 2: Generate full AI response using Vertex AI
            ai_response = await self.voice_bot.gemini_service.generate_response(
                text_input, 
                self.voice_bot.conversation_context.get(session_id, [])
            )
            
            if ai_response:
                # Update conversation history
                history = self.voice_bot.conversation_context.get(session_id, [])
                history.append({"role": "user", "content": text_input})
                history.append({"role": "assistant", "content": ai_response})
                self.voice_bot.conversation_context[session_id] = history[-10:]  # Keep last 10 messages
                
                # Generate audio response
                response_audio = await self.voice_bot.tts_service.synthesize_speech(ai_response)
                
                if response_audio:
                    # Send response back to UI
                    response_message = {
                        'type': 'audio',
                        'data': base64.b64encode(response_audio).decode('utf-8'),
                        'text': ai_response,
                        'customer_intent': 'ai_conversation',
                        'next_action': 'continue'
                    }
                    await websocket.send(self.voice_bot._serialize_message(response_message))
                    logger.info(f"📤 Sent AI response to UI {session_id}: {ai_response}")
                    
        except Exception as e:
            logger.error(f"❌ Error handling text message: {e}")
    
    async def handle_init_signal(self, websocket: WebSocketServerProtocol, session_id: str):
        """Handle init signal from UI"""
        try:
            logger.info(f"🚀 Init signal from UI {session_id} - sending greeting")
            await self._send_greeting(websocket, session_id)
                
        except Exception as e:
            logger.error(f"❌ Error handling init signal: {e}")

    async def handle_signal_message(self, websocket: WebSocketServerProtocol, session_id: str, data: dict):
        """Handle signal messages from UI for Phase 1 testing"""
        try:
            signal_type = data.get('data', '')
            logger.info(f"📡 Received signal from UI {session_id}: {signal_type}")
            
            if signal_type == 'DIALER_GREETING':
                # Phase 1: AI greeting first (dialer integration)
                logger.info(f"🎤 Starting dialer greeting for session {session_id}")
                await self._handle_dialer_greeting(websocket, session_id)
                
            elif signal_type == 'CUSTOMER_YES':
                # Phase 1: Customer responds "Yes"
                logger.info(f"✅ Handling customer 'Yes' response for session {session_id}")
                await self._handle_customer_yes(websocket, session_id)
                
            elif signal_type == 'CUSTOMER_NO':
                # Phase 1: Customer responds "No"
                logger.info(f"❌ Handling customer 'No' response for session {session_id}")
                await self._handle_customer_no(websocket, session_id)
                
            elif signal_type == 'NORMAL_CONVERSATION':
                # Phase 1: Normal conversation mode
                logger.info(f"💬 Starting normal conversation for session {session_id}")
                await self._handle_normal_conversation_start(websocket, session_id)
                
            else:
                logger.warning(f"⚠️ Unknown signal type: {signal_type}")
                
        except Exception as e:
            logger.error(f"❌ Error handling signal message: {e}")
            logger.error(f"❌ Signal data: {data}")

    async def _handle_dialer_greeting(self, websocket: WebSocketServerProtocol, session_id: str):
        """Handle Phase 1 dialer greeting scenario"""
        try:
            logger.info(f"🎤 Phase 1: Starting dialer greeting for session {session_id}")
            
            # Send AI greeting first
            await self._send_greeting(websocket, session_id)
            
            # Mark greeting as sent
            self.voice_bot._greeting_sent[session_id] = True
            
        except Exception as e:
            logger.error(f"❌ Error handling dialer greeting: {e}")

    async def _handle_customer_yes(self, websocket: WebSocketServerProtocol, session_id: str):
        """Handle Phase 1 customer 'Yes' response using AI intent analysis"""
        try:
            logger.info(f"✅ Phase 1: Customer responded 'Yes' in session {session_id}")
            
            # Use AI to analyze intent (no keyword checking)
            ai_intent = await self.voice_bot.gemini_service.analyze_intent("yes")
            logger.info(f"🤖 AI intent analysis for 'yes': {ai_intent}")
            
            # Get appropriate response based on AI analysis
            if ai_intent == "INTERESTED":
                intent_response = self.voice_bot.intent_analyzer.phase1_responses["interested"]
            elif ai_intent == "NOT_INTERESTED":
                intent_response = self.voice_bot.intent_analyzer.phase1_responses["not_interested"]
            else:
                intent_response = self.voice_bot.intent_analyzer.phase1_responses["unclear"]
            
            if intent_response:
                # Convert to speech
                tts_start = time.time()
                response_audio = await self.voice_bot.tts_service.synthesize_speech(intent_response['message'])
                tts_time = time.time() - tts_start
                
                if response_audio:
                    # Log response
                    self.voice_bot.conversation_logger.log_ai_response(
                        session_id, 
                        intent_response['message'], 
                        "ai_intent_analysis", 
                        tts_time, 
                        len(response_audio)
                    )
                    
                    # Send response to UI
                    response_message = {
                        'type': 'audio',
                        'data': base64.b64encode(response_audio).decode('utf-8'),
                        'text': intent_response['message'],
                        'customer_intent': ai_intent,
                        'next_action': intent_response.get('action', 'continue')
                    }
                    await websocket.send(self.voice_bot._serialize_message(response_message))
                    logger.info(f"📤 Sent Phase 1 AI-analyzed response to UI {session_id}: {ai_intent}")
                    
        except Exception as e:
            logger.error(f"❌ Error handling customer 'Yes': {e}")

    async def _handle_customer_no(self, websocket: WebSocketServerProtocol, session_id: str):
        """Handle Phase 1 customer 'No' response using AI intent analysis"""
        try:
            logger.info(f"❌ Phase 1: Customer responded 'No' in session {session_id}")
            
            # Use AI to analyze intent (no keyword checking)
            ai_intent = await self.voice_bot.gemini_service.analyze_intent("no")
            logger.info(f"🤖 AI intent analysis for 'no': {ai_intent}")
            
            # Get appropriate response based on AI analysis
            if ai_intent == "INTERESTED":
                intent_response = self.voice_bot.intent_analyzer.phase1_responses["interested"]
            elif ai_intent == "NOT_INTERESTED":
                intent_response = self.voice_bot.intent_analyzer.phase1_responses["not_interested"]
            else:
                intent_response = self.voice_bot.intent_analyzer.phase1_responses["unclear"]
            
            if intent_response:
                # Convert to speech
                tts_start = time.time()
                response_audio = await self.voice_bot.tts_service.synthesize_speech(intent_response['message'])
                tts_time = time.time() - tts_start
                
                if response_audio:
                    # Log response
                    self.voice_bot.conversation_logger.log_ai_response(
                        session_id, 
                        intent_response['message'], 
                        "ai_intent_analysis", 
                        tts_time, 
                        len(response_audio)
                    )
                    
                    # Send response to UI
                    response_message = {
                        'type': 'audio',
                        'data': base64.b64encode(response_audio).decode('utf-8'),
                        'text': intent_response['message'],
                        'customer_intent': ai_intent,
                        'next_action': intent_response.get('action', 'continue')
                    }
                    await websocket.send(self.voice_bot._serialize_message(response_message))
                    logger.info(f"📤 Sent Phase 1 AI-analyzed response to UI {session_id}: {ai_intent}")
                    
        except Exception as e:
            logger.error(f"❌ Error handling customer 'No': {e}")

    async def _handle_normal_conversation_start(self, websocket: WebSocketServerProtocol, session_id: str):
        """Handle Phase 1 normal conversation start"""
        try:
            logger.info(f"💬 Phase 1: Started normal conversation mode for session {session_id}")
            
            # Send confirmation message
            confirmation_text = "Normal conversation mode activated. You can now speak or use the text buttons to interact with the AI."
            
            # Convert to speech
            tts_start = time.time()
            response_audio = await self.voice_bot.tts_service.synthesize_speech(confirmation_text)
            tts_time = time.time() - tts_start
            
            if response_audio:
                # Log response
                self.voice_bot.conversation_logger.log_ai_response(
                    session_id, 
                    confirmation_text, 
                    "conversation_start", 
                    tts_time, 
                    len(response_audio)
                )
                
                # Send response to UI
                response_message = {
                    'type': 'audio',
                    'data': base64.b64encode(response_audio).decode('utf-8'),
                    'text': confirmation_text,
                    'customer_intent': 'conversation_start',
                    'next_action': 'continue'
                }
                await websocket.send(self.voice_bot._serialize_message(response_message))
                logger.info(f"📤 Sent normal conversation start message to UI {session_id}")
                
        except Exception as e:
            logger.error(f"❌ Error handling normal conversation start: {e}")
    
    async def _send_greeting(self, websocket: WebSocketServerProtocol, session_id: str):
        """Send the Policybazaar greeting for UI testing"""
        try:
            logger.info(f"📞 Sending Policybazaar greeting for UI session {session_id}")
            
            # Get greeting from Phase 1 responses
            greeting_response = self.voice_bot.intent_analyzer.get_greeting_response()
            greeting_text = greeting_response['message']
            logger.info(f"📝 Greeting text: {greeting_text}")
            
            # Convert to speech
            tts_start = time.time()
            response_audio = await self.voice_bot.tts_service.synthesize_speech(greeting_text)
            tts_time = time.time() - tts_start
            
            if response_audio:
                logger.info(f"🔊 TTS successful: {len(response_audio)} bytes in {tts_time:.2f}s")
                
                # Log greeting response
                self.voice_bot.conversation_logger.log_ai_response(
                    session_id, 
                    greeting_text, 
                    "greeting", 
                    tts_time, 
                    len(response_audio)
                )
                
                # Send greeting to UI
                greeting_message = {
                    'type': 'audio',
                    'data': base64.b64encode(response_audio).decode('utf-8'),
                    'text': greeting_text,
                    'customer_intent': 'greeting',
                    'next_action': 'continue'
                }
                await websocket.send(self.voice_bot._serialize_message(greeting_message))
                logger.info(f"📤 Sent Policybazaar greeting to UI {session_id}")
                
            else:
                logger.error(f"❌ TTS failed for greeting in session {session_id}")
                
        except Exception as e:
            logger.error(f"❌ Error sending greeting: {e}")
            logger.error(f"❌ Exception details: {type(e).__name__}: {str(e)}") 