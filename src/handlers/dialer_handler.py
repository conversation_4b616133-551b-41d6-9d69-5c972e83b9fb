"""
Dialer handler for processing dialer-specific messages
"""

import base64
import logging
import time
from typing import Dict, Any, Optional

from websockets.server import WebSocketServerProtocol

logger = logging.getLogger(__name__)


class DialerHandler:
    """
    Handler for dialer integration messages
    
    Features:
    - Audio processing for dialer format
    - Intent analysis for Phase 1
    - Response generation
    - Session management
    """
    
    def __init__(self, voice_bot):
        """Initialize dialer handler"""
        self.voice_bot = voice_bot
        logger.info("📞 Dialer handler initialized")
    
    async def handle_audio_message(self, websocket: WebSocketServerProtocol, session_id: str, data: dict):
        """Handle audio messages from dialer"""
        try:
            # Initialize session state if needed
            self.voice_bot._initialize_session_state(session_id)
            self.voice_bot._update_session_activity(session_id)
            
            audio_b64 = data.get('data', '')
            if not audio_b64:
                logger.warning(f"⚠️ No audio data in dialer message from {session_id}")
                return
            
            audio_chunk = base64.b64decode(audio_b64)
            logger.info(f"🎤 Received dialer audio chunk: {len(audio_chunk)} bytes from {session_id}")
            
            # Log audio received
            audio_metadata = {
                "size_bytes": len(audio_chunk),
                "format": "base64_encoded",
                "source": "dialer",
                "sample_rate": 8000,  # Dialer format
                "channels": 1
            }
            self.voice_bot.conversation_logger.log_audio_received(session_id, audio_metadata)
            
            # Convert audio format
            converted_audio = await self.voice_bot.audio_converter.convert_dialer_audio_format(audio_chunk)
            
            # Process the audio
            process_start = time.time()
            response = await self._process_dialer_audio(converted_audio, session_id)
            process_time = time.time() - process_start
            logger.info(f"✅ Processed dialer audio in {process_time:.2f}s, response: {response is not None}")
            
            if response:
                # VDR: Set speaking state before sending audio
                session_state = self.voice_bot._session_states[session_id]
                session_state.is_speaking = True
                session_state.interrupted = False
                
                # Send audio response back to dialer
                response_message = {
                    'type': 'audio',
                    'data': response['audio_b64'],
                    'text': response.get('ai_response', ''),
                    'customer_intent': response.get('transcribed_text', ''),
                    'next_action': response.get('action', 'continue')
                }
                await websocket.send(self.voice_bot._serialize_message(response_message))
                logger.info(f"📤 Sent audio response to dialer {session_id}")
                
                # VDR: Clear speaking state after sending
                session_state = self.voice_bot._session_states[session_id]
                session_state.is_speaking = False
                
                # Check if session should end
                if response.get('action') == 'end_call':
                    await self.voice_bot._send_session_end_signal(websocket, session_id, response.get('reason', 'call_completed'))
                    return
                    
        except Exception as e:
            logger.error(f"❌ Error handling dialer audio message: {e}")
    
    async def _process_dialer_audio(self, audio_data: bytes, session_id: str) -> Optional[dict]:
        """Process dialer audio and generate appropriate response"""
        try:
            start_time = time.time()
            logger.info(f"🎤 Processing dialer audio for {session_id} ({len(audio_data)} bytes)")
            
            # Perform speech recognition
            stt_result = await self.voice_bot.speech_service.recognize_speech_with_metadata(audio_data, session_id)
            if not stt_result:
                logger.warning(f"⚠️ No speech detected in dialer audio for {session_id}")
                return None
            
            transcript = stt_result["transcript"]
            confidence = stt_result["confidence"]
            stt_time = stt_result["processing_time"]
            logger.info(f"📝 Dialer transcription: '{transcript}' (confidence: {confidence:.2f})")
            
            # Log speech recognition
            self.voice_bot.conversation_logger.log_speech_recognition(session_id, transcript, confidence, stt_time)
            
            # Phase 1: Analyze customer response for simple intent detection
            ai_start = time.time()
            response_text = await self.voice_bot.intent_analyzer.analyze_customer_response(transcript, session_id)
            ai_time = time.time() - ai_start
            
            if response_text:
                # Convert to speech
                tts_start = time.time()
                response_audio = await self.voice_bot.tts_service.synthesize_speech(response_text['message'])
                tts_time = time.time() - tts_start
                
                if response_audio:
                    # Log AI response
                    response_type = response_text.get('reason', 'unknown')
                    self.voice_bot.conversation_logger.log_ai_response(
                        session_id, 
                        response_text['message'], 
                        response_type, 
                        tts_time, 
                        len(response_audio)
                    )
                    
                    return {
                        'audio_b64': base64.b64encode(response_audio).decode('utf-8'),
                        'transcribed_text': transcript,
                        'ai_response': response_text['message'],
                        'action': response_text.get('action'),
                        'reason': response_text.get('reason')
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error processing dialer audio: {e}")
            return None
    
    async def handle_start_event(self, websocket: WebSocketServerProtocol, session_id: str, data: dict):
        """Handle start event from dialer"""
        try:
            logger.info(f"🚀 Start event received from dialer {session_id}")
            
            # Set session mode to dialer integration
            self.voice_bot._session_modes[session_id] = 'dialer_integration'
            logger.info(f"🎯 Set session {session_id} to dialer_integration mode")
            
            # Send greeting immediately
            if session_id not in self.voice_bot._greeting_sent or not self.voice_bot._greeting_sent[session_id]:
                self.voice_bot._greeting_sent[session_id] = True
                await self._send_greeting(websocket, session_id)
                
        except Exception as e:
            logger.error(f"❌ Error handling start event: {e}")
    
    async def _send_greeting(self, websocket: WebSocketServerProtocol, session_id: str):
        """Send the Policybazaar greeting when customer answers the call"""
        try:
            logger.info(f"📞 Sending Policybazaar greeting for session {session_id}")
            
            # Get greeting from Phase 1 responses
            greeting_response = self.voice_bot.intent_analyzer.get_greeting_response()
            greeting_text = greeting_response['message']
            
            # Convert to speech
            tts_start = time.time()
            response_audio = await self.voice_bot.tts_service.synthesize_speech(greeting_text)
            tts_time = time.time() - tts_start
            
            if response_audio:
                # Log greeting response
                self.voice_bot.conversation_logger.log_ai_response(
                    session_id, 
                    greeting_text, 
                    "greeting", 
                    tts_time, 
                    len(response_audio)
                )
                
                # Send greeting to dialer
                greeting_message = {
                    'type': 'audio',
                    'data': base64.b64encode(response_audio).decode('utf-8'),
                    'text': greeting_text,
                    'customer_intent': 'greeting',
                    'next_action': 'continue'
                }
                await websocket.send(self.voice_bot._serialize_message(greeting_message))
                logger.info(f"📤 Sent Policybazaar greeting to dialer {session_id}")
                
        except Exception as e:
            logger.error(f"❌ Error sending greeting: {e}")
    
    async def handle_timeout_signal(self, websocket: WebSocketServerProtocol, session_id: str):
        """Handle dialer timeout"""
        try:
            logger.info(f"⏰ Timeout signal from dialer {session_id}")
            
            # Send a retry prompt
            prompt_text = "I'm sorry, I didn't hear you. Are you looking for assistance with your insurance needs today?"
            
            response_audio = await self.voice_bot.tts_service.synthesize_speech(prompt_text)
            
            if response_audio:
                response_message = {
                    'type': 'audio',
                    'data': base64.b64encode(response_audio).decode('utf-8'),
                    'ai_response': prompt_text
                }
                await websocket.send(self.voice_bot._serialize_message(response_message))
                
        except Exception as e:
            logger.error(f"❌ Error handling dialer timeout: {e}") 