"""
Session state model for voice bot sessions
"""

import time
from typing import Dict, Any, Optional
from dataclasses import dataclass, field


@dataclass
class SessionState:
    """
    Session state for voice bot sessions
    
    Attributes:
        session_id: Unique session identifier
        is_speaking: Whether the bot is currently speaking
        is_listening: Whether the bot is listening for input
        interrupted: Whether the bot was interrupted during speech
        current_task: Current task being performed
        last_activity: Timestamp of last activity
        session_mode: Mode of the session (dialer_integration, normal_conversation)
        greeting_sent: Whether greeting has been sent
        conversation_context: Conversation history
    """
    
    session_id: str
    is_speaking: bool = False
    is_listening: bool = True
    interrupted: bool = False
    current_task: Optional[str] = None
    last_activity: float = field(default_factory=time.time)
    session_mode: str = "normal_conversation"
    greeting_sent: bool = False
    conversation_context: list = field(default_factory=list)
    
    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = time.time()
    
    def is_expired(self, timeout_seconds: int = 300) -> bool:
        """Check if session has expired"""
        return time.time() - self.last_activity > timeout_seconds
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert session state to dictionary"""
        return {
            "session_id": self.session_id,
            "is_speaking": self.is_speaking,
            "is_listening": self.is_listening,
            "interrupted": self.interrupted,
            "current_task": self.current_task,
            "last_activity": self.last_activity,
            "session_mode": self.session_mode,
            "greeting_sent": self.greeting_sent,
            "conversation_context_length": len(self.conversation_context)
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SessionState':
        """Create session state from dictionary"""
        return cls(
            session_id=data["session_id"],
            is_speaking=data.get("is_speaking", False),
            is_listening=data.get("is_listening", True),
            interrupted=data.get("interrupted", False),
            current_task=data.get("current_task"),
            last_activity=data.get("last_activity", time.time()),
            session_mode=data.get("session_mode", "normal_conversation"),
            greeting_sent=data.get("greeting_sent", False),
            conversation_context=data.get("conversation_context", [])
        ) 