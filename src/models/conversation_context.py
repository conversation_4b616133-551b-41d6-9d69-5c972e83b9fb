"""
Conversation context model for managing conversation history
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from datetime import datetime


@dataclass
class ConversationMessage:
    """
    Individual conversation message
    
    Attributes:
        role: Role of the speaker (user, assistant)
        content: Message content
        timestamp: Message timestamp
        message_type: Type of message (text, audio, intent)
    """
    
    role: str
    content: str
    timestamp: datetime = field(default_factory=datetime.utcnow)
    message_type: str = "text"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary"""
        return {
            "role": self.role,
            "content": self.content,
            "timestamp": self.timestamp.isoformat(),
            "message_type": self.message_type
        }


@dataclass
class ConversationContext:
    """
    Conversation context for voice bot sessions
    
    Attributes:
        session_id: Session identifier
        messages: List of conversation messages
        max_history: Maximum number of messages to keep
        current_intent: Current detected intent
        conversation_summary: Summary of the conversation
    """
    
    session_id: str
    messages: List[ConversationMessage] = field(default_factory=list)
    max_history: int = 20
    current_intent: Optional[str] = None
    conversation_summary: Dict[str, Any] = field(default_factory=dict)
    
    def add_message(self, role: str, content: str, message_type: str = "text"):
        """Add a new message to the conversation"""
        message = ConversationMessage(role=role, content=content, message_type=message_type)
        self.messages.append(message)
        
        # Keep only the last max_history messages
        if len(self.messages) > self.max_history:
            self.messages = self.messages[-self.max_history:]
    
    def get_recent_messages(self, count: int = 10) -> List[ConversationMessage]:
        """Get recent messages from the conversation"""
        return self.messages[-count:] if self.messages else []
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get summary of the conversation"""
        if not self.messages:
            return {"message_count": 0, "last_activity": None}
        
        return {
            "message_count": len(self.messages),
            "last_activity": self.messages[-1].timestamp.isoformat(),
            "user_messages": len([m for m in self.messages if m.role == "user"]),
            "assistant_messages": len([m for m in self.messages if m.role == "assistant"]),
            "current_intent": self.current_intent
        }
    
    def clear(self):
        """Clear conversation history"""
        self.messages.clear()
        self.current_intent = None
        self.conversation_summary.clear()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert conversation context to dictionary"""
        return {
            "session_id": self.session_id,
            "messages": [msg.to_dict() for msg in self.messages],
            "max_history": self.max_history,
            "current_intent": self.current_intent,
            "conversation_summary": self.get_conversation_summary()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConversationContext':
        """Create conversation context from dictionary"""
        messages = []
        for msg_data in data.get("messages", []):
            message = ConversationMessage(
                role=msg_data["role"],
                content=msg_data["content"],
                timestamp=datetime.fromisoformat(msg_data["timestamp"]),
                message_type=msg_data.get("message_type", "text")
            )
            messages.append(message)
        
        return cls(
            session_id=data["session_id"],
            messages=messages,
            max_history=data.get("max_history", 20),
            current_intent=data.get("current_intent"),
            conversation_summary=data.get("conversation_summary", {})
        ) 