"""
Main Voice Bot class that orchestrates all services and handlers
"""

import asyncio
import logging
import os
import sys
import time
import traceback
from typing import Dict, Any, Optional

# Import services
from src.services.logging import ConversationLogger
from src.services.ai import GeminiService, IntentAnalyzer
from src.services.audio import SpeechService, TTSService, AudioConverter
from src.services.websocket import WebSocketServer

# Import handlers
from src.handlers.dialer_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.handlers.ui_handler import UIHandler

# Import utils
from src.utils.message_utils import MessageUtils
from src.utils.session_utils import SessionUtils

# Import configuration
from config import (
    GOOGLE_CLOUD_PROJECT, SERVICE_ACCOUNT_FILE,
    SERVER_CONFIG, LOGGING_CONFIG, PERFORMANCE_CONFIG
)

logger = logging.getLogger(__name__)


class PolicyBazaarVoiceBot:
    """
    Professional Voice Bot for PolicyBazaar Outbound Calls
    
    Features:
    - Phase 1: Simple intent detection (YES/NO) with hardcoded responses
    - Phase 2: Full AI conversations (future implementation)
    - Multi-language support
    - Dialer integration
    - UI testing interface
    - Modular architecture
    """
    
    def __init__(self):
        """Initialize the voice bot with all required services"""
        try:
            logger.info("🚀 Initializing PolicyBazaar Voice Bot...")
            
            # Set Google Cloud credentials
            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = SERVICE_ACCOUNT_FILE
            
            # Initialize services
            self._initialize_services()
            
            # Initialize handlers
            self._initialize_handlers()
            
            # Initialize utilities
            self._initialize_utilities()
            
            # Initialize conversation tracking
            self.conversation_context = {}
            self._session_start_times = {}
            
            # Initialize session management (delegated to session_utils)
            # These are accessed through self.session_utils._session_states and self.session_utils._session_modes
            
            logger.info("✅ PolicyBazaar Voice Bot initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize voice bot: {e}")
            raise
    
    @property
    def _session_states(self):
        """Delegate to session utilities"""
        return self.session_utils._session_states
    
    @property
    def _session_modes(self):
        """Delegate to session utilities"""
        return self.session_utils._session_modes
    
    @property
    def _greeting_sent(self):
        """Delegate to session utilities"""
        return self.session_utils._greeting_sent
    
    def _initialize_services(self):
        """Initialize all services"""
        logger.info("🔧 Initializing services...")
        
        # Initialize logging service
        self.conversation_logger = ConversationLogger()
        
        # Initialize AI services
        self.gemini_service = GeminiService()
        self.intent_analyzer = IntentAnalyzer(self.gemini_service)
        
        # Initialize audio services
        self.speech_service = SpeechService()
        self.tts_service = TTSService()
        self.audio_converter = AudioConverter()
        
        # Initialize WebSocket server
        self.websocket_server = WebSocketServer(self)
        
        logger.info("✅ All services initialized")
    
    def _initialize_handlers(self):
        """Initialize message handlers"""
        logger.info("🔧 Initializing handlers...")
        
        self.dialer_handler = DialerHandler(self)
        self.ui_handler = UIHandler(self)
        
        logger.info("✅ All handlers initialized")
    
    def _initialize_utilities(self):
        """Initialize utility classes"""
        logger.info("🔧 Initializing utilities...")
        
        self.message_utils = MessageUtils()
        self.session_utils = SessionUtils()
        
        logger.info("✅ All utilities initialized")
    
    def _initialize_session_state(self, session_id: str):
        """Initialize session state for VDR management"""
        return self.session_utils.initialize_session_state(session_id)
    
    def _update_session_activity(self, session_id: str):
        """Update session activity timestamp"""
        self.session_utils.update_session_activity(session_id)
    
    def _cleanup_expired_sessions(self):
        """Clean up expired sessions"""
        return self.session_utils.cleanup_expired_sessions()
    
    def _parse_message(self, message: str, session_id: str) -> Optional[Dict[str, Any]]:
        """Parse WebSocket message"""
        return self.message_utils.parse_message(message, session_id)
    
    def _serialize_message(self, data: Dict[str, Any]) -> str:
        """Serialize message to JSON"""
        return self.message_utils.serialize_message(data)
    
    async def _route_message(self, websocket, session_id: str, data: dict):
        """Route message to appropriate handler"""
        try:
            # Determine message type
            message_type = self.message_utils.get_message_type(data)
            logger.info(f"📋 Message type: {message_type} for {session_id}")
            
            # Route based on message type
            if message_type == 'audio':
                await self._handle_audio_message(websocket, session_id, data)
            elif message_type == 'text':
                await self._handle_text_message(websocket, session_id, data)
            elif message_type == 'signal':
                await self._handle_signal_message(websocket, session_id, data)
            elif message_type == 'event_start':
                await self._handle_start_event(websocket, session_id, data)
            else:
                logger.warning(f"⚠️ Unknown message type: {message_type}")
                
        except Exception as e:
            logger.error(f"❌ Error routing message: {e}")
            self.conversation_logger.log_error(session_id, "message_routing_error", str(e), traceback.format_exc())
    
    async def _handle_audio_message(self, websocket, session_id: str, data: dict):
        """Handle audio messages"""
        try:
            # Clean up expired sessions periodically
            current_time = time.time()
            if current_time - self.session_utils._last_cleanup_time > 60:  # Every minute
                self._cleanup_expired_sessions()
                self.session_utils._last_cleanup_time = current_time
            
            # Determine session mode and route to appropriate handler
            session_mode = self.session_utils.get_session_mode(session_id)
            
            if session_mode == 'dialer_integration':
                await self.dialer_handler.handle_audio_message(websocket, session_id, data)
            else:
                await self.ui_handler.handle_audio_message(websocket, session_id, data)
                
        except Exception as e:
            logger.error(f"❌ Error handling audio message: {e}")
    
    async def _handle_text_message(self, websocket, session_id: str, data: dict):
        """Handle text messages"""
        try:
            await self.ui_handler.handle_text_message(websocket, session_id, data)
        except Exception as e:
            logger.error(f"❌ Error handling text message: {e}")
    
    async def _handle_signal_message(self, websocket, session_id: str, data: dict):
        """Handle signal messages"""
        try:
            signal_data = data.get('data', '')
            logger.info(f"📡 Received signal: {signal_data} from {session_id}")
            
            if signal_data == '__INIT__':
                await self.ui_handler.handle_init_signal(websocket, session_id)
            elif signal_data == '__TIMEOUT__':
                await self.dialer_handler.handle_timeout_signal(websocket, session_id)
            elif signal_data == '__SESSION_END__':
                logger.info(f"📞 Session end signal from {session_id}")
                return
            else:
                # Handle custom UI signals
                await self.ui_handler.handle_signal_message(websocket, session_id, data)
                    
        except Exception as e:
            logger.error(f"❌ Error handling signal message: {e}")
    
    async def _handle_start_event(self, websocket, session_id: str, data: dict):
        """Handle start event from dialer"""
        try:
            await self.dialer_handler.handle_start_event(websocket, session_id, data)
        except Exception as e:
            logger.error(f"❌ Error handling start event: {e}")
    
    async def _send_session_end_signal(self, websocket, session_id: str, reason: str = 'completed'):
        """Send session end signal to dialer"""
        try:
            # Log session end
            if session_id in self._session_start_times:
                total_duration = time.time() - self._session_start_times[session_id]
                conversation_summary = {
                    "total_messages": len(self.conversation_context.get(session_id, [])),
                    "session_mode": self.session_utils.get_session_mode(session_id),
                    "greeting_sent": self.session_utils.is_greeting_sent(session_id),
                    "end_reason": reason
                }
                self.conversation_logger.log_session_end(session_id, reason, total_duration, conversation_summary)
            
            end_signal = self.message_utils.create_signal_message('__SESSION_END__', reason)
            await websocket.send(self._serialize_message(end_signal))
            logger.info(f"📞 Sent session end signal to dialer {session_id}: {reason}")
            
        except Exception as e:
            logger.error(f"❌ Error sending session end signal: {e}")
    
    async def start_server(self):
        """Start the WebSocket server"""
        try:
            await self.websocket_server.start_server()
        except Exception as e:
            logger.error(f"❌ Failed to start server: {e}")
            raise
    
    def get_session_logs(self, session_id: str):
        """Get logs for a specific session"""
        return self.conversation_logger.get_session_logs(session_id)
    
    def get_conversation_summary(self, session_id: str):
        """Get conversation summary for debugging"""
        return self.conversation_logger.get_conversation_summary(session_id)
    
    def get_server_stats(self):
        """Get server statistics"""
        return self.websocket_server.get_server_stats()
    
    def get_session_stats(self):
        """Get session statistics"""
        return self.session_utils.get_session_stats()


# ============================================================================
# MAIN FUNCTION
# ============================================================================

async def main():
    """Main function to start the voice bot"""
    voice_bot = None
    try:
        logger.info("🎤 PolicyBazaar Voice Bot - Professional Outbound Call System")
        logger.info("=" * 80)
        logger.info("📋 Phase 1: Simple intent detection (YES/NO) with hardcoded responses")
        logger.info("📋 Phase 2: Full AI conversations (future implementation)")
        logger.info("=" * 80)
        
        # Create voice bot instance
        voice_bot = PolicyBazaarVoiceBot()
        logger.info("✅ Voice bot instance created successfully")
        
        # Start server
        await voice_bot.start_server()
        
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)
    finally:
        # Cleanup MongoDB connection
        if voice_bot and hasattr(voice_bot, 'conversation_logger'):
            voice_bot.conversation_logger.close_connection()
            logger.info("🧹 MongoDB connection closed")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 Voice bot stopped")
    except Exception as e:
        logger.error(f"❌ Failed to start voice bot: {e}")
        sys.exit(1) 