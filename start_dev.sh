#!/bin/bash

# PolicyBazaar Voice Bot - Development Startup Script
# Starts both the voice bot server and React client

echo "🎤 PolicyBazaar Voice Bot - Development Environment"
echo "=================================================="

# Function to cleanup background processes
cleanup() {
    echo ""
    echo "🛑 Shutting down development environment..."
    kill $SERVER_PID $CLIENT_PID 2>/dev/null
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip install -r requirements.txt

# Create logs directory
mkdir -p logs

# Set environment variables
export GOOGLE_APPLICATION_CREDENTIALS="credentials/google-service-account.json"
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Start the voice bot server in background
echo "🚀 Starting voice bot server..."
python main.py &
SERVER_PID=$!

# Wait a moment for server to start
sleep 3

# Check if client directory exists
if [ ! -d "client" ]; then
    echo "❌ React client not found. Please run the setup first."
    kill $SERVER_PID
    exit 1
fi

# Navigate to client directory and start React app
echo "🌐 Starting React client..."
cd client

# Install client dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing client dependencies..."
    npm install
fi

# Start React development server
npm start &
CLIENT_PID=$!

# Wait for both processes
echo ""
echo "✅ Development environment started!"
echo "📍 Voice Bot Server: ws://localhost:8765"
echo "🌐 React Client: http://localhost:3000"
echo ""
echo "Press Ctrl+C to stop both servers"
echo ""

# Wait for background processes
wait $SERVER_PID $CLIENT_PID 