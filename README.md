# PolicyBazaar Voice Bot

A professional, modular voice bot system for outbound calls with AI integration.

## 🏗️ Architecture

This project uses a modular architecture with clear separation of concerns:

```
src/
├── services/           # Core business logic
│   ├── logging/       # MongoDB conversation logging
│   ├── ai/           # AI services (Gemini, intent analysis)
│   ├── audio/        # Audio processing (STT, TTS, conversion)
│   └── websocket/    # WebSocket server management
├── handlers/          # Message processing
├── models/           # Data structures
├── utils/            # Utility functions
└── voice_bot.py      # Main orchestrator
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Node.js 16+ (for React client)
- Google Cloud credentials
- MongoDB connection

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd voice-bot1
   ```

2. **Set up Python environment**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Configure credentials**
   - Place your Google Cloud service account key as `credentials/google-service-account.json`
   - Update `config.py` with your MongoDB connection string

4. **Start the development environment**
   ```bash
   # Start both server and React client
   ./start_dev.sh
   
   # Or start individually:
   # Server only
   python main.py
   
   # Client only (in client/)
npm start
   ```

## 📋 Features

### Phase 1: Simple Intent Detection
- **Dialer Integration**: Processes outbound call audio
- **Intent Analysis**: Detects customer interest (YES/NO)
- **Hardcoded Responses**: Professional PolicyBazaar responses
- **Session Management**: Tracks call state and flow

### Phase 2: Full AI Conversations (Future)
- **Full AI Integration**: Complete conversation capabilities
- **Advanced Intent Detection**: Multi-intent classification
- **Context Management**: Conversation history tracking
- **Multi-language Support**: Multiple language processing

### Professional Testing Interface
- **React-based UI**: Modern, responsive testing interface
- **Real-time Communication**: WebSocket-based messaging
- **Audio Recording**: Browser-based audio capture
- **Conversation Logging**: Real-time message display
- **Connection Monitoring**: WebSocket status tracking

## 🔧 Configuration

### Core Configuration (`config.py`)
- **Google Cloud**: Project settings and credentials
- **MongoDB**: Connection and logging configuration
- **Server**: WebSocket server settings
- **AI**: Gemini model parameters
- **Audio**: Speech recognition and TTS settings

### Environment Variables
```bash
export GOOGLE_APPLICATION_CREDENTIALS="credentials/google-service-account.json"
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

## 📊 Monitoring

### Logs
- **Application Logs**: `logs/voice_bot.log`
- **MongoDB Logs**: Conversation tracking and analytics
- **Performance Metrics**: Processing times and statistics

### WebSocket Server
- **Address**: `ws://localhost:8765`
- **Status**: Real-time connection monitoring
- **Sessions**: Active session tracking

### React Client
- **Address**: `http://localhost:3000`
- **Features**: Real-time testing interface
- **Audio**: Browser-based recording and playback

## 🧪 Testing

### React Testing Interface
- **Modern UI**: Professional React-based interface
- **Audio Testing**: Real-time audio recording and playback
- **Text Testing**: Direct text input for testing
- **Connection Status**: Real-time WebSocket monitoring
- **Conversation Log**: Live message display

### Dialer Integration
- **Audio Processing**: 8kHz PCM audio handling
- **Intent Detection**: Phase 1 simple intent analysis
- **Response Generation**: Professional call flow management

## 📁 Project Structure

```
voice-bot1/
├── src/                          # Main source code
│   ├── services/                 # Core services
│   ├── handlers/                 # Message handlers
│   ├── models/                   # Data models
│   ├── utils/                    # Utility functions
│   └── voice_bot.py             # Main orchestrator
├── client/                       # React testing client
├── main.py                       # Entry point
├── config.py                     # Configuration
├── requirements.txt              # Dependencies
├── start_production.sh          # Production script
├── start_dev.sh                 # Development script

├── logs/                        # Application logs
├── credentials/                 # Google Cloud credentials
├── venv/                        # Virtual environment
└── README.md                    # This file
```

## 🔄 Development

### Adding New Services
1. Create service in `src/services/`
2. Implement required interfaces
3. Add to main orchestrator
4. Add tests and documentation

### Adding New Handlers
1. Create handler in `src/handlers/`
2. Implement message processing
3. Register with main voice bot
4. Add routing logic

### React Client Development
1. Navigate to `client/`
2. Install dependencies: `npm install`
3. Start development server: `npm start`
4. Make changes in `src/` directory
5. Build for production: `npm run build`

### Code Style
- Follow PEP 8 guidelines
- Add comprehensive docstrings
- Include type hints
- Write unit tests

## 📚 Documentation

- **Client**: `client/README.md`
- **API Documentation**: In development
- **Deployment Guide**: In development

## 🚀 Production Deployment

### Using Production Script
```bash
./start_production.sh
```

### Manual Deployment
```bash
# Set up environment
source venv/bin/activate
export GOOGLE_APPLICATION_CREDENTIALS="credentials/google-service-account.json"

# Start server
python main.py
```

### Development Environment
```bash
# Start both server and React client
./start_dev.sh
```

### Docker Deployment (Future)
```bash
# Build image
docker build -t voice-bot .

# Run container
docker run -p 8765:8765 -p 3000:3000 voice-bot
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is proprietary to PolicyBazaar.

## 🆘 Support

For support and questions:
- Check the documentation
- Review the logs
- Contact the development team

---

**PolicyBazaar Voice Bot** - Professional Outbound Call System 