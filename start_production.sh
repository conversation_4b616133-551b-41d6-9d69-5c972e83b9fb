#!/bin/bash

# PolicyBazaar Voice Bot - Production Startup Script
# Modular Architecture Version

echo "🎤 PolicyBazaar Voice Bot - Production Startup"
echo "================================================"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install/update dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Create logs directory if it doesn't exist
mkdir -p logs

# Set environment variables
export GOOGLE_APPLICATION_CREDENTIALS="credentials/google-service-account.json"
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Start the voice bot
echo "🚀 Starting PolicyBazaar Voice Bot..."
echo "📍 Server will be available at: ws://localhost:8765"
echo "📊 Logs will be written to: logs/voice_bot.log"
echo ""

# Run the voice bot
python main.py

echo ""
echo "�� Voice Bot stopped" 