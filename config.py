"""
Configuration file for PolicyBazaar Voice Bot
"""

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT = "named-defender-182511"
SERVICE_ACCOUNT_FILE = "credentials/google-service-account.json"

# Audio Configuration
AUDIO_CONFIG = {
    "sample_rate": 16000,
    "channels": 1,
    "encoding": "LINEAR16",  # 16-bit PCM
    "chunk_duration_ms": 100,  # Process 100ms chunks for low latency
    "silence_threshold_ms": 1000,  # 1 second of silence to detect end of speech
    "speech_detection_ms": 300,  # 300ms of speech to detect start of speech
    "interruption_threshold_ms": 500,  # 500ms to detect interruption
    "max_audio_chunk_size": 3200,  # 200ms of audio at 16kHz 16-bit mono
}

# Speech Recognition Configuration
SPEECH_CONFIG = {
    "language_code": "en-IN",  # Indian English
    "enable_automatic_punctuation": True,
    "enable_word_time_offsets": False,
    "model": "latest_long",
    "use_enhanced": True,
    "enable_interim_results": True,  # Enable interim results for real-time processing
    "interim_results_config": {
        "enable_interim_results": True,
        "max_alternatives": 1
    }
}

# Text-to-Speech Configuration
TTS_CONFIG = {
    "language_code": "en-IN",
    "voice_name": "en-IN-Standard-A",
    "ssml_gender": "FEMALE",
    "audio_encoding": "LINEAR16",
    "sample_rate_hertz": 8000,  # Reduced from 16000 to make audio smaller
    "speaking_rate": 1.0,  # Normal speaking rate
    "pitch": 0.0,  # Normal pitch
    "volume_gain_db": 0.0  # Normal volume
}

# AI Configuration
AI_CONFIG = {
    "project": "named-defender-182511",  # Google Cloud project ID
    "model": "gemini-2.5-flash",
    "temperature": 0.7,
    "max_output_tokens": 2048,  # Increased from 1024 to 2048 for better responses
    "top_p": 0.8,
    "context_messages": 5,  # Keep last 5 messages for context
    "max_history": 10,  # Keep last 10 messages in session
    "interruption_response": "I apologize for the interruption. Please continue with your question.",
    "timeout_response": "I didn't hear you clearly. Could you please repeat your question?"
}

# System Prompt for Policybazaar
SYSTEM_PROMPT = """You are a friendly and professional Policybazaar customer service representative. 
Your goal is to have natural, engaging conversations with customers about their insurance needs.

Key Guidelines:
- Be conversational and friendly, not robotic
- Respond naturally to what customers say
- Ask follow-up questions to understand their needs better
- Only connect to an advisor when customer explicitly asks or shows clear interest
- Keep responses concise but engaging (under 100 words)
- Use both Hindi and English as appropriate

Intent Detection:
- If customer says "connect me to advisor", "speak to advisor", "talk to advisor" → Connect to advisor
- If customer says "no", "not interested", "busy", "call later" → Politely end call
- If customer mentions insurance needs → Provide helpful information and ask follow-up questions
- For everything else → Respond naturally and engage in conversation

Conversation Flow:
1. Greet and introduce yourself naturally
2. Respond to customer's information (name, age, etc.) with interest
3. Ask relevant questions about their insurance needs
4. Provide helpful information about insurance options
5. Only connect to advisor when customer shows clear intent

Example responses:
- Customer: "Mera naam Deepak hai" → "Namaste Deepak ji! Kaise hain aap?"
- Customer: "Mai 22 saal ka hoo" → "22 saal ki umar mein insurance ke bare mein sochna bahut achha hai. Kya aap insurance ke bare mein janna chahte hain?"
- Customer: "Travel insurance lena hai" → "Travel insurance ke liye aapko advisor se connect karta hoon..."
- Customer: "Connect me to advisor" → "Great! Connecting you to a certified insurance advisor who can assist you further..."

Focus on: Health, motor, life, and travel insurance. Be helpful and conversational!"""

# Outbound Call System Prompt for Policybazaar
OUTBOUND_CALL_PROMPT = """You are a professional voice assistant representing Policybazaar, India's leading insurance platform. Your job is to interact with customers over phone calls in a friendly, polite, and professional tone. Keep responses short and direct, suitable for real-time telephonic conversation. Always identify the customer's intent clearly and avoid vague or open-ended answers. If the customer sounds interested, offer to connect them with a certified insurance advisor. If the customer is uninterested or says 'no', end the call politely. If interrupted, acknowledge politely and ask them to continue.

Important: You can understand and respond to both English and Hindi/Urdu responses from customers. Pay attention to common patterns like "mujhe kal call karna" (call me tomorrow) which indicates the customer wants to end the call."""

# Dialer Integration Configuration
DIALER_CONFIG = {
    "audio_format": {
        "sample_rate": 8000,  # 8kHz as per dialer team's specification
        "channels": 1,        # Mono
        "sample_width": 2,    # 16-bit
        "chunk_size": 320,    # 320 bytes = 20ms of audio at 8kHz 16-bit mono
        "encoding": "LINEAR16"
    },
    "websocket": {
        "endpoint": "/ws",    # Endpoint that dialer team will connect to
        "ping_interval": 20,
        "ping_timeout": 10,
        "close_timeout": 10
    },
    "audio_processing": {
        "buffer_chunks": 10,  # Process audio every 10 chunks
        "max_silence_duration": 5.0,  # Maximum silence before timeout
        "speech_timeout": 30.0,        # Maximum time to wait for speech
        "interruption_detection": True,  # Enable interruption detection
        "auto_response_detection": True  # Enable automatic response detection
    },
    "signals": {
        "timeout": "__TIMEOUT__",
        "session_end": "__SESSION_END__", 
        "interrupt": "__INTERRUPT__",
        "speech_start": "__SPEECH_START__",
        "speech_end": "__SPEECH_END__"
    }
}

# Server Configuration
SERVER_CONFIG = {
    "websocket": {
        "host": "0.0.0.0",  # Changed from localhost for production
        "port": 8765,
        "ping_interval": 20,
        "ping_timeout": 20,
    },
    "rest_api": {
        "host": "0.0.0.0",
        "port": 8000,
    }
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "logs/voice_bot.log",  # Added file logging for production
    "max_bytes": 10485760,  # 10MB
    "backup_count": 5,
}

# Performance Configuration
PERFORMANCE_CONFIG = {
    "max_concurrent_sessions": 100,
    "session_timeout_seconds": 300,  # 5 minutes
    "audio_buffer_size": 8192,
    "enable_caching": True,
    "cache_ttl_seconds": 3600,  # 1 hour
    "max_connections": 200,  # Added max connections for production
    "connection_timeout": 30,  # Added connection timeout
    "real_time_processing": True,  # Enable real-time audio processing
    "interruption_handling": True,  # Enable interruption handling
    "auto_speech_detection": True  # Enable automatic speech detection
}

# MongoDB Configuration
MONGODB_CONFIG = {
    "connection_string": "mongodb://oneLeadUsr:0neLea98jnjdhg3!@20.80.40.117:27017/?authSource=oneLeadDB",
    "database_name": "oneLeadDB",
    "collection_name": "voice_bot_conversations",
    "connection_timeout": 5000,  # 5 seconds
    "server_selection_timeout": 5000,  # 5 seconds
    "max_pool_size": 10,
    "min_pool_size": 1,
    "max_idle_time": 30000,  # 30 seconds
    "retry_writes": True,
    "retry_reads": True
}

# Conversation Logging Configuration
CONVERSATION_LOGGING = {
    "enabled": True,
    "log_audio_metadata": True,  # Log audio size, duration, format
    "log_ai_responses": True,    # Log AI-generated responses
    "log_customer_intent": True, # Log detected customer intent
    "log_performance_metrics": True,  # Log processing times
    "log_errors": True,          # Log errors and exceptions
    "log_session_events": True,  # Log session start/end events
    "max_log_size_mb": 16,       # Maximum log document size
    "log_retention_days": 90     # Keep logs for 90 days
}

# Error Messages
ERROR_MESSAGES = {
    "audio_processing": "I apologize, but I'm having trouble processing your audio. Please try again.",
    "ai_generation": "I apologize, but I'm having trouble generating a response. Please try again.",
    "tts_error": "I apologize, but I'm having trouble converting my response to speech. Please try again.",
    "network_error": "I apologize, but there's a network issue. Please try again.",
    "session_expired": "Your session has expired. Please start a new conversation.",
    "interruption": "I apologize for the interruption. Please continue with your question.",
    "timeout": "I didn't hear you clearly. Could you please repeat your question?",
    "mongodb_connection": "Failed to connect to MongoDB for logging",
    "mongodb_insert": "Failed to save conversation log to MongoDB"
} 