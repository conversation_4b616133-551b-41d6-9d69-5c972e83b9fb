#!/usr/bin/env python3
"""
PolicyBazaar Voice Bot - Professional Outbound Call System

Main entry point for the modular voice bot system.
"""

import asyncio
import logging
import sys
import traceback

# Configure logging
from config import LOGGING_CONFIG

logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG["level"]),
    format=LOGGING_CONFIG["format"],
    handlers=[
        logging.FileHandler(LOGGING_CONFIG["file"]),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Import the voice bot
from src.voice_bot import PolicyBazaarVoiceBot


async def main():
    """Main function to start the voice bot"""
    voice_bot = None
    try:
        logger.info("🎤 PolicyBazaar Voice Bot - Professional Outbound Call System")
        logger.info("=" * 80)
        logger.info("📋 Phase 1: Simple intent detection (YES/NO) with hardcoded responses")
        logger.info("📋 Phase 2: Full AI conversations (future implementation)")
        logger.info("🏗️ Modular Architecture: Services, Handlers, Models, Utils")
        logger.info("=" * 80)
        
        # Create voice bot instance
        voice_bot = PolicyBazaarVoiceBot()
        logger.info("✅ Voice bot instance created successfully")
        
        # Start server
        await voice_bot.start_server()
        
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)
    finally:
        # Cleanup MongoDB connection
        if voice_bot and hasattr(voice_bot, 'conversation_logger'):
            voice_bot.conversation_logger.close_connection()
            logger.info("🧹 MongoDB connection closed")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 Voice bot stopped")
    except Exception as e:
        logger.error(f"❌ Failed to start voice bot: {e}")
        sys.exit(1) 